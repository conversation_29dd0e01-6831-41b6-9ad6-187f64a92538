"""
Coinbase Pro exchange connector for ArbitrageVision.

This module implements the Coinbase Pro exchange interface with WebSocket streaming
and REST API integration.
"""

import asyncio
import json
import time
import base64
import hmac
import hashlib
from typing import Dict, List, Optional, Any
import websockets
import aiohttp
import structlog

from src.exchanges.base import (
    BaseExchange, OrderBook, OrderBookLevel, Trade, ConnectionStatus
)
from src.config.settings import ExchangeConfig


class CoinbaseExchange(BaseExchange):
    """Coinbase Pro exchange connector."""
    
    def __init__(self, config: ExchangeConfig):
        super().__init__(config)
        self.logger = structlog.get_logger("CoinbaseExchange")
        
        # Coinbase Pro specific settings
        self.base_url = config.base_url or "https://api.pro.coinbase.com"
        self.ws_url = config.ws_url or "wss://ws-feed.pro.coinbase.com"
        
        if config.sandbox:
            self.base_url = "https://api-public.sandbox.pro.coinbase.com"
            self.ws_url = "wss://ws-feed-public.sandbox.pro.coinbase.com"
        
        # Authentication
        self.api_key = config.api_key
        self.api_secret = config.api_secret
        self.passphrase = config.passphrase
        
        # WebSocket subscriptions
        self.subscribed_symbols: set = set()
        self.subscribed_channels: Dict[str, List[str]] = {
            "level2": [],
            "matches": []
        }
        
        # Session for HTTP requests
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def connect(self) -> bool:
        """Connect to Coinbase Pro WebSocket and REST API."""
        try:
            self._update_status(ConnectionStatus.CONNECTING)
            
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30)
            )
            
            # Test REST API connection
            await self._test_connectivity()
            
            # Connect WebSocket
            await self._connect_websocket()
            
            self._update_status(ConnectionStatus.CONNECTED)
            self.logger.info("Connected to Coinbase Pro", exchange=self.name)
            return True
            
        except Exception as e:
            self.logger.error("Failed to connect to Coinbase Pro", error=str(e))
            self._update_status(ConnectionStatus.ERROR)
            return False
    
    async def disconnect(self) -> None:
        """Disconnect from Coinbase Pro."""
        try:
            # Close WebSocket connection
            if self.ws_connection:
                await self.ws_connection.close()
                self.ws_connection = None
            
            # Close HTTP session
            if self.session:
                await self.session.close()
                self.session = None
            
            self._update_status(ConnectionStatus.DISCONNECTED)
            self.logger.info("Disconnected from Coinbase Pro", exchange=self.name)
            
        except Exception as e:
            self.logger.error("Error disconnecting from Coinbase Pro", error=str(e))
    
    async def subscribe_order_book(self, symbol: str) -> bool:
        """Subscribe to order book updates for a symbol."""
        try:
            coinbase_symbol = self.denormalize_symbol(symbol)
            
            if coinbase_symbol not in self.subscribed_channels["level2"]:
                self.subscribed_channels["level2"].append(coinbase_symbol)
                self.subscribed_symbols.add(symbol)
                
                # If WebSocket is connected, subscribe to the channel
                if self.ws_connection:
                    await self._subscribe_channel("level2", [coinbase_symbol])
                
                self.logger.info("Subscribed to order book", symbol=symbol)
                return True
            
            return True
            
        except Exception as e:
            self.logger.error("Failed to subscribe to order book", symbol=symbol, error=str(e))
            return False
    
    async def unsubscribe_order_book(self, symbol: str) -> bool:
        """Unsubscribe from order book updates for a symbol."""
        try:
            coinbase_symbol = self.denormalize_symbol(symbol)
            
            if coinbase_symbol in self.subscribed_channels["level2"]:
                self.subscribed_channels["level2"].remove(coinbase_symbol)
                self.subscribed_symbols.discard(symbol)
                
                # If WebSocket is connected, unsubscribe from the channel
                if self.ws_connection:
                    await self._unsubscribe_channel("level2", [coinbase_symbol])
                
                self.logger.info("Unsubscribed from order book", symbol=symbol)
                return True
            
            return True
            
        except Exception as e:
            self.logger.error("Failed to unsubscribe from order book", symbol=symbol, error=str(e))
            return False
    
    async def subscribe_trades(self, symbol: str) -> bool:
        """Subscribe to trade updates for a symbol."""
        try:
            coinbase_symbol = self.denormalize_symbol(symbol)
            
            if coinbase_symbol not in self.subscribed_channels["matches"]:
                self.subscribed_channels["matches"].append(coinbase_symbol)
                
                # If WebSocket is connected, subscribe to the channel
                if self.ws_connection:
                    await self._subscribe_channel("matches", [coinbase_symbol])
                
                self.logger.info("Subscribed to trades", symbol=symbol)
                return True
            
            return True
            
        except Exception as e:
            self.logger.error("Failed to subscribe to trades", symbol=symbol, error=str(e))
            return False
    
    async def unsubscribe_trades(self, symbol: str) -> bool:
        """Unsubscribe from trade updates for a symbol."""
        try:
            coinbase_symbol = self.denormalize_symbol(symbol)
            
            if coinbase_symbol in self.subscribed_channels["matches"]:
                self.subscribed_channels["matches"].remove(coinbase_symbol)
                
                # If WebSocket is connected, unsubscribe from the channel
                if self.ws_connection:
                    await self._unsubscribe_channel("matches", [coinbase_symbol])
                
                self.logger.info("Unsubscribed from trades", symbol=symbol)
                return True
            
            return True
            
        except Exception as e:
            self.logger.error("Failed to unsubscribe from trades", symbol=symbol, error=str(e))
            return False
    
    async def get_order_book_snapshot(self, symbol: str, depth: int = 20) -> Optional[OrderBook]:
        """Get order book snapshot from REST API."""
        try:
            if not self.session:
                return None
            
            coinbase_symbol = self.denormalize_symbol(symbol)
            url = f"{self.base_url}/products/{coinbase_symbol}/book"
            params = {"level": 2}
            
            headers = self._get_auth_headers("GET", f"/products/{coinbase_symbol}/book", "")
            
            async with self.session.get(url, params=params, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_order_book_snapshot(symbol, data)
                else:
                    self.logger.error("Failed to get order book snapshot", 
                                    symbol=symbol, status=response.status)
                    return None
                    
        except Exception as e:
            self.logger.error("Error getting order book snapshot", symbol=symbol, error=str(e))
            return None
    
    async def get_trading_fees(self, symbol: str) -> Dict[str, float]:
        """Get trading fees for a symbol."""
        try:
            if not self.session:
                return {"maker": 0.005, "taker": 0.005}
            
            url = f"{self.base_url}/fees"
            headers = self._get_auth_headers("GET", "/fees", "")
            
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        "maker": float(data.get("maker_fee_rate", 0.005)),
                        "taker": float(data.get("taker_fee_rate", 0.005))
                    }
                else:
                    # Return default fees
                    return {"maker": 0.005, "taker": 0.005}
                    
        except Exception as e:
            self.logger.error("Error getting trading fees", symbol=symbol, error=str(e))
            return {"maker": 0.005, "taker": 0.005}
    
    def normalize_symbol(self, symbol: str) -> str:
        """Convert Coinbase symbol to normalized format (e.g., BTC-USD -> BTC/USD)."""
        return symbol.replace("-", "/")
    
    def denormalize_symbol(self, symbol: str) -> str:
        """Convert normalized symbol to Coinbase format (e.g., BTC/USD -> BTC-USD)."""
        return symbol.replace("/", "-")
    
    async def _test_connectivity(self) -> None:
        """Test REST API connectivity."""
        url = f"{self.base_url}/time"
        async with self.session.get(url) as response:
            if response.status != 200:
                raise Exception(f"Coinbase Pro API connectivity test failed: {response.status}")
    
    async def _connect_websocket(self) -> None:
        """Connect to Coinbase Pro WebSocket."""
        try:
            self.ws_connection = await websockets.connect(self.ws_url)
            
            # Start WebSocket message handler
            self.ws_task = asyncio.create_task(self._handle_websocket_messages())
            
            # Subscribe to existing channels
            await self._resubscribe_all()
            
        except Exception as e:
            self.logger.error("Failed to connect WebSocket", error=str(e))
            raise
    
    async def _handle_websocket_messages(self) -> None:
        """Handle incoming WebSocket messages."""
        try:
            async for message in self.ws_connection:
                try:
                    data = json.loads(message)
                    await self._process_websocket_message(data)
                except json.JSONDecodeError as e:
                    self.logger.error("Failed to parse WebSocket message", error=str(e))
                except Exception as e:
                    self.logger.error("Error processing WebSocket message", error=str(e))
                    
        except websockets.exceptions.ConnectionClosed:
            self.logger.warning("WebSocket connection closed")
            self._update_status(ConnectionStatus.DISCONNECTED)
            # Attempt to reconnect
            await self._reconnect()
        except Exception as e:
            self.logger.error("WebSocket error", error=str(e))
            self._update_status(ConnectionStatus.ERROR)
    
    async def _process_websocket_message(self, data: Dict[str, Any]) -> None:
        """Process WebSocket message."""
        message_type = data.get("type")
        
        if message_type == "snapshot":
            await self._handle_snapshot(data)
        elif message_type == "l2update":
            await self._handle_l2_update(data)
        elif message_type == "match":
            await self._handle_match(data)
        elif message_type == "error":
            self.logger.error("WebSocket error message", error=data.get("message"))
    
    async def _handle_snapshot(self, data: Dict[str, Any]) -> None:
        """Handle order book snapshot."""
        try:
            symbol = self.normalize_symbol(data["product_id"])
            order_book = self._parse_order_book_snapshot(symbol, data)
            if order_book:
                self._notify_order_book_update(order_book)
                
        except Exception as e:
            self.logger.error("Error handling snapshot", error=str(e))
    
    async def _handle_l2_update(self, data: Dict[str, Any]) -> None:
        """Handle level 2 order book update."""
        try:
            symbol = self.normalize_symbol(data["product_id"])
            order_book = self._parse_order_book_update(symbol, data)
            if order_book:
                self._notify_order_book_update(order_book)
                
        except Exception as e:
            self.logger.error("Error handling l2 update", error=str(e))
    
    async def _handle_match(self, data: Dict[str, Any]) -> None:
        """Handle trade match."""
        try:
            symbol = self.normalize_symbol(data["product_id"])
            trade = self._parse_trade(symbol, data)
            if trade:
                self._notify_trade_update(trade)
                
        except Exception as e:
            self.logger.error("Error handling match", error=str(e))

    def _parse_order_book_snapshot(self, symbol: str, data: Dict[str, Any]) -> OrderBook:
        """Parse order book snapshot."""
        timestamp = time.time()

        bids = [
            OrderBookLevel(price=float(bid[0]), quantity=float(bid[1]), timestamp=timestamp)
            for bid in data.get("bids", [])
        ]

        asks = [
            OrderBookLevel(price=float(ask[0]), quantity=float(ask[1]), timestamp=timestamp)
            for ask in data.get("asks", [])
        ]

        return OrderBook(
            symbol=symbol,
            exchange=self.name,
            bids=bids,
            asks=asks,
            timestamp=timestamp
        )

    def _parse_order_book_update(self, symbol: str, data: Dict[str, Any]) -> Optional[OrderBook]:
        """Parse order book update."""
        try:
            timestamp = time.time()

            # Get current order book
            current_ob = self.get_order_book(symbol)
            if not current_ob:
                return None

            # Apply changes
            bids = current_ob.bids.copy()
            asks = current_ob.asks.copy()

            for change in data.get("changes", []):
                side, price_str, size_str = change
                price = float(price_str)
                size = float(size_str)

                if side == "buy":
                    # Update bids
                    if size == 0:
                        bids = [b for b in bids if b.price != price]
                    else:
                        updated = False
                        for i, bid in enumerate(bids):
                            if bid.price == price:
                                bids[i] = OrderBookLevel(price=price, quantity=size, timestamp=timestamp)
                                updated = True
                                break
                        if not updated:
                            bids.append(OrderBookLevel(price=price, quantity=size, timestamp=timestamp))

                elif side == "sell":
                    # Update asks
                    if size == 0:
                        asks = [a for a in asks if a.price != price]
                    else:
                        updated = False
                        for i, ask in enumerate(asks):
                            if ask.price == price:
                                asks[i] = OrderBookLevel(price=price, quantity=size, timestamp=timestamp)
                                updated = True
                                break
                        if not updated:
                            asks.append(OrderBookLevel(price=price, quantity=size, timestamp=timestamp))

            # Sort order book
            bids.sort(key=lambda x: x.price, reverse=True)
            asks.sort(key=lambda x: x.price)

            return OrderBook(
                symbol=symbol,
                exchange=self.name,
                bids=bids,
                asks=asks,
                timestamp=timestamp
            )

        except Exception as e:
            self.logger.error("Error parsing order book update", symbol=symbol, error=str(e))
            return None

    def _parse_trade(self, symbol: str, data: Dict[str, Any]) -> Optional[Trade]:
        """Parse trade from match message."""
        try:
            return Trade(
                symbol=symbol,
                exchange=self.name,
                price=float(data["price"]),
                quantity=float(data["size"]),
                side=data["side"],
                timestamp=time.time(),
                trade_id=data.get("trade_id")
            )

        except Exception as e:
            self.logger.error("Error parsing trade", symbol=symbol, error=str(e))
            return None

    async def _subscribe_channel(self, channel: str, product_ids: List[str]) -> None:
        """Subscribe to a WebSocket channel."""
        if self.ws_connection:
            message = {
                "type": "subscribe",
                "channels": [{"name": channel, "product_ids": product_ids}]
            }

            # Add authentication if available
            if self.api_key and self.api_secret and self.passphrase:
                timestamp = str(time.time())
                message_str = timestamp + "GET" + "/users/self/verify"
                signature = self._sign_message(message_str)

                message.update({
                    "signature": signature,
                    "key": self.api_key,
                    "passphrase": self.passphrase,
                    "timestamp": timestamp
                })

            await self.ws_connection.send(json.dumps(message))

    async def _unsubscribe_channel(self, channel: str, product_ids: List[str]) -> None:
        """Unsubscribe from a WebSocket channel."""
        if self.ws_connection:
            message = {
                "type": "unsubscribe",
                "channels": [{"name": channel, "product_ids": product_ids}]
            }
            await self.ws_connection.send(json.dumps(message))

    async def _resubscribe_all(self) -> None:
        """Resubscribe to all channels after reconnection."""
        for channel, product_ids in self.subscribed_channels.items():
            if product_ids:
                await self._subscribe_channel(channel, product_ids)

    def _get_auth_headers(self, method: str, path: str, body: str) -> Dict[str, str]:
        """Generate authentication headers for REST API."""
        if not (self.api_key and self.api_secret and self.passphrase):
            return {}

        timestamp = str(time.time())
        message = timestamp + method + path + body
        signature = self._sign_message(message)

        return {
            "CB-ACCESS-KEY": self.api_key,
            "CB-ACCESS-SIGN": signature,
            "CB-ACCESS-TIMESTAMP": timestamp,
            "CB-ACCESS-PASSPHRASE": self.passphrase,
            "Content-Type": "application/json"
        }

    def _sign_message(self, message: str) -> str:
        """Sign message with API secret."""
        secret = base64.b64decode(self.api_secret)
        signature = hmac.new(secret, message.encode(), hashlib.sha256)
        return base64.b64encode(signature.digest()).decode()

    async def _send_ping(self) -> None:
        """Send ping to WebSocket connection."""
        if self.ws_connection:
            await self.ws_connection.ping()
