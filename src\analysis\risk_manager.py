"""
Risk management framework for ArbitrageVision.

This module provides comprehensive risk management including position limits,
correlation monitoring, drawdown protection, and emergency controls.
"""

import asyncio
import time
from typing import Dict, List, Optional, Callable, Set
from collections import defaultdict, deque
from dataclasses import dataclass
import numpy as np
import structlog

from src.config.settings import Settings
from src.data.models import (
    ArbitrageOpportunity, Position, RiskMetrics, Alert, 
    PerformanceMetrics, ArbitrageType
)
from src.analysis.profitability import ProfitabilityAnalyzer, ProfitabilityAnalysis


@dataclass
class RiskLimits:
    """Risk limits configuration."""
    max_position_size: float
    max_total_exposure: float
    max_exchange_exposure: float
    max_correlation_exposure: float
    max_daily_drawdown: float
    max_total_drawdown: float
    max_latency_threshold: float


@dataclass
class RiskAssessment:
    """Risk assessment result."""
    overall_risk_score: float
    risk_factors: Dict[str, float]
    risk_level: str
    recommendations: List[str]
    approved: bool
    max_position_size: float


class RiskManager:
    """Comprehensive risk management system."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.config = settings.risk_management
        self.logger = structlog.get_logger("RiskManager")
        
        # Risk limits
        self.risk_limits = RiskLimits(
            max_position_size=self.config.max_pair_exposure,
            max_total_exposure=self.config.max_total_exposure,
            max_exchange_exposure=self.config.max_exchange_exposure,
            max_correlation_exposure=self.config.max_correlation_exposure,
            max_daily_drawdown=self.config.max_daily_drawdown,
            max_total_drawdown=self.config.max_total_drawdown,
            max_latency_threshold=self.config.max_latency_threshold
        )
        
        # Current positions
        self.positions: Dict[str, Position] = {}
        
        # Exposure tracking
        self.exchange_exposure: Dict[str, float] = defaultdict(float)
        self.symbol_exposure: Dict[str, float] = defaultdict(float)
        self.total_exposure: float = 0
        
        # P&L tracking
        self.daily_pnl: float = 0
        self.total_pnl: float = 0
        self.daily_start_time: float = time.time()
        self.pnl_history: deque = deque(maxlen=1000)
        
        # Correlation tracking
        self.correlation_matrix: Dict[str, Dict[str, float]] = defaultdict(dict)
        self.price_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # Latency monitoring
        self.latency_measurements: Dict[str, deque] = defaultdict(lambda: deque(maxlen=50))
        
        # Alert handlers
        self.alert_handlers: List[Callable[[Alert], None]] = []
        
        # Emergency state
        self.emergency_stop: bool = False
        self.circuit_breaker_triggered: bool = False
        
        # Performance tracking
        self.risk_assessments_count: int = 0
        self.opportunities_blocked: int = 0
        self.alerts_generated: int = 0
        
        # Running state
        self.running: bool = False
        self.monitor_task: Optional[asyncio.Task] = None
        
        # Profitability analyzer
        self.profitability_analyzer = ProfitabilityAnalyzer(settings)
        
    async def initialize(self) -> None:
        """Initialize the risk manager."""
        self.logger.info("Initializing risk manager")
        
        # Reset daily P&L if new day
        self._check_daily_reset()
        
        self.logger.info("Risk manager initialized")
    
    async def start(self) -> None:
        """Start the risk manager."""
        self.logger.info("Starting risk manager")
        
        self.running = True
        
        # Start monitoring task
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        
        self.logger.info("Risk manager started")
    
    async def stop(self) -> None:
        """Stop the risk manager."""
        self.logger.info("Stopping risk manager")
        
        self.running = False
        
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Risk manager stopped")
    
    def is_running(self) -> bool:
        """Check if risk manager is running."""
        return self.running
    
    def add_alert_handler(self, handler: Callable[[Alert], None]) -> None:
        """Add alert handler."""
        self.alert_handlers.append(handler)
    
    def remove_alert_handler(self, handler: Callable[[Alert], None]) -> None:
        """Remove alert handler."""
        if handler in self.alert_handlers:
            self.alert_handlers.remove(handler)
    
    async def evaluate_opportunity(self, opportunity: ArbitrageOpportunity) -> Optional[ArbitrageOpportunity]:
        """Evaluate and potentially approve an arbitrage opportunity."""
        try:
            self.risk_assessments_count += 1
            
            # Check emergency stop
            if self.emergency_stop or self.circuit_breaker_triggered:
                self.opportunities_blocked += 1
                return None
            
            # Perform risk assessment
            risk_assessment = await self._assess_opportunity_risk(opportunity)
            
            if not risk_assessment.approved:
                self.opportunities_blocked += 1
                self.logger.info("Opportunity blocked by risk management",
                               opportunity_id=opportunity.id,
                               risk_score=risk_assessment.overall_risk_score,
                               risk_level=risk_assessment.risk_level)
                return None
            
            # Adjust position size if needed
            if risk_assessment.max_position_size < opportunity.quantity:
                opportunity.quantity = risk_assessment.max_position_size
                # Recalculate profit with adjusted quantity
                opportunity.profit_absolute = (opportunity.sell_price - opportunity.buy_price) * opportunity.quantity
                opportunity.profit_percentage = (opportunity.profit_absolute / (opportunity.buy_price * opportunity.quantity)) * 100
            
            # Add risk score to metadata
            opportunity.risk_score = risk_assessment.overall_risk_score
            
            return opportunity
            
        except Exception as e:
            self.logger.error("Error evaluating opportunity risk", 
                            opportunity_id=opportunity.id, 
                            error=str(e))
            return None
    
    async def _assess_opportunity_risk(self, opportunity: ArbitrageOpportunity) -> RiskAssessment:
        """Assess risk for an arbitrage opportunity."""
        risk_factors = {}
        recommendations = []
        
        # Position size risk
        position_value = opportunity.buy_price * opportunity.quantity
        position_risk = min(position_value / self.risk_limits.max_position_size, 1.0)
        risk_factors["position_size"] = position_risk
        
        if position_risk > 0.8:
            recommendations.append("Consider reducing position size")
        
        # Exchange exposure risk
        buy_exposure = self.exchange_exposure[opportunity.buy_exchange] + position_value
        sell_exposure = self.exchange_exposure[opportunity.sell_exchange] + position_value
        
        max_exchange_risk = max(
            buy_exposure / self.risk_limits.max_exchange_exposure,
            sell_exposure / self.risk_limits.max_exchange_exposure
        )
        risk_factors["exchange_exposure"] = min(max_exchange_risk, 1.0)
        
        if max_exchange_risk > 0.8:
            recommendations.append("High exchange exposure detected")
        
        # Total exposure risk
        total_exposure_risk = (self.total_exposure + position_value) / self.risk_limits.max_total_exposure
        risk_factors["total_exposure"] = min(total_exposure_risk, 1.0)
        
        if total_exposure_risk > 0.8:
            recommendations.append("Approaching total exposure limit")
        
        # Correlation risk
        correlation_risk = self._calculate_correlation_risk(opportunity)
        risk_factors["correlation"] = correlation_risk
        
        if correlation_risk > 0.7:
            recommendations.append("High correlation with existing positions")
        
        # Drawdown risk
        drawdown_risk = self._calculate_drawdown_risk()
        risk_factors["drawdown"] = drawdown_risk
        
        if drawdown_risk > 0.8:
            recommendations.append("High drawdown detected")
        
        # Latency risk
        latency_risk = self._calculate_latency_risk(opportunity)
        risk_factors["latency"] = latency_risk
        
        if latency_risk > 0.7:
            recommendations.append("High latency detected")
        
        # Market risk (based on opportunity characteristics)
        market_risk = self._calculate_market_risk(opportunity)
        risk_factors["market"] = market_risk
        
        # Calculate overall risk score
        overall_risk_score = sum(risk_factors.values()) / len(risk_factors)
        
        # Determine risk level
        if overall_risk_score < 0.3:
            risk_level = "low"
        elif overall_risk_score < 0.6:
            risk_level = "medium"
        elif overall_risk_score < 0.8:
            risk_level = "high"
        else:
            risk_level = "very_high"
        
        # Approval decision
        approved = overall_risk_score < 0.8 and not self.emergency_stop
        
        # Calculate maximum allowed position size
        max_position_size = self._calculate_max_position_size(opportunity, risk_factors)
        
        return RiskAssessment(
            overall_risk_score=overall_risk_score,
            risk_factors=risk_factors,
            risk_level=risk_level,
            recommendations=recommendations,
            approved=approved,
            max_position_size=max_position_size
        )
    
    def _calculate_correlation_risk(self, opportunity: ArbitrageOpportunity) -> float:
        """Calculate correlation risk with existing positions."""
        if not self.positions:
            return 0.0
        
        # Simplified correlation calculation
        symbol_correlations = []
        
        for position in self.positions.values():
            if position.symbol == opportunity.symbol:
                symbol_correlations.append(1.0)  # Perfect correlation
            else:
                # Get correlation from matrix or estimate
                correlation = self.correlation_matrix.get(opportunity.symbol, {}).get(position.symbol, 0.0)
                symbol_correlations.append(abs(correlation))
        
        if not symbol_correlations:
            return 0.0
        
        # Return maximum correlation
        return max(symbol_correlations)
    
    def _calculate_drawdown_risk(self) -> float:
        """Calculate current drawdown risk."""
        # Daily drawdown risk
        daily_drawdown_risk = abs(self.daily_pnl) / (self.risk_limits.max_daily_drawdown * self.total_exposure) if self.total_exposure > 0 else 0
        
        # Total drawdown risk (simplified)
        total_drawdown_risk = abs(self.total_pnl) / (self.risk_limits.max_total_drawdown * self.total_exposure) if self.total_exposure > 0 else 0
        
        return max(daily_drawdown_risk, total_drawdown_risk)
    
    def _calculate_latency_risk(self, opportunity: ArbitrageOpportunity) -> float:
        """Calculate latency risk for exchanges."""
        buy_latencies = self.latency_measurements.get(opportunity.buy_exchange, [])
        sell_latencies = self.latency_measurements.get(opportunity.sell_exchange, [])
        
        if not buy_latencies or not sell_latencies:
            return 0.5  # Unknown latency
        
        avg_buy_latency = sum(buy_latencies) / len(buy_latencies)
        avg_sell_latency = sum(sell_latencies) / len(sell_latencies)
        
        max_latency = max(avg_buy_latency, avg_sell_latency)
        
        return min(max_latency / self.risk_limits.max_latency_threshold, 1.0)
    
    def _calculate_market_risk(self, opportunity: ArbitrageOpportunity) -> float:
        """Calculate market risk based on opportunity characteristics."""
        risk_factors = []
        
        # Profit margin risk (very high profits might be errors)
        if opportunity.profit_percentage > 5.0:  # 5%
            risk_factors.append(0.8)
        elif opportunity.profit_percentage > 2.0:  # 2%
            risk_factors.append(0.4)
        else:
            risk_factors.append(0.1)
        
        # Confidence risk
        confidence_risk = 1.0 - opportunity.confidence
        risk_factors.append(confidence_risk)
        
        # Arbitrage type risk
        type_risk = {
            ArbitrageType.SIMPLE: 0.2,
            ArbitrageType.TRIANGULAR: 0.4,
            ArbitrageType.STATISTICAL: 0.6,
            ArbitrageType.BASIS: 0.5
        }.get(opportunity.type, 0.5)
        risk_factors.append(type_risk)
        
        return sum(risk_factors) / len(risk_factors)
    
    def _calculate_max_position_size(self, opportunity: ArbitrageOpportunity, risk_factors: Dict[str, float]) -> float:
        """Calculate maximum allowed position size."""
        base_max_size = opportunity.quantity
        
        # Apply risk-based scaling
        risk_scaling = 1.0 - (sum(risk_factors.values()) / len(risk_factors)) * 0.5
        
        # Apply exposure limits
        position_value = opportunity.buy_price * base_max_size
        
        # Exchange exposure limit
        buy_available = max(0, self.risk_limits.max_exchange_exposure - self.exchange_exposure[opportunity.buy_exchange])
        sell_available = max(0, self.risk_limits.max_exchange_exposure - self.exchange_exposure[opportunity.sell_exchange])
        exchange_limit = min(buy_available, sell_available) / opportunity.buy_price
        
        # Total exposure limit
        total_available = max(0, self.risk_limits.max_total_exposure - self.total_exposure)
        total_limit = total_available / opportunity.buy_price
        
        # Position size limit
        position_limit = self.risk_limits.max_position_size / opportunity.buy_price
        
        # Apply all limits
        max_size = min(base_max_size, exchange_limit, total_limit, position_limit) * risk_scaling
        
        return max(0, max_size)
    
    def update_latency(self, exchange: str, latency: float) -> None:
        """Update latency measurement for an exchange."""
        self.latency_measurements[exchange].append(latency)
        
        # Check for latency spikes
        if latency > self.risk_limits.max_latency_threshold:
            self._generate_alert(
                "latency_spike",
                "warning",
                f"High latency detected on {exchange}",
                f"Latency: {latency:.2f}ms (threshold: {self.risk_limits.max_latency_threshold}ms)",
                {"exchange": exchange, "latency": latency}
            )
    
    def update_position(self, position: Position) -> None:
        """Update position information."""
        self.positions[position.id] = position
        
        # Update exposure tracking
        self._recalculate_exposures()
        
        # Update P&L
        self.daily_pnl += position.unrealized_pnl
        self.total_pnl += position.unrealized_pnl
        
        # Check for circuit breaker conditions
        self._check_circuit_breaker()
    
    def remove_position(self, position_id: str) -> None:
        """Remove a position."""
        if position_id in self.positions:
            del self.positions[position_id]
            self._recalculate_exposures()
    
    def _recalculate_exposures(self) -> None:
        """Recalculate all exposure metrics."""
        self.exchange_exposure.clear()
        self.symbol_exposure.clear()
        self.total_exposure = 0
        
        for position in self.positions.values():
            exposure = position.market_value
            self.exchange_exposure[position.exchange] += exposure
            self.symbol_exposure[position.symbol] += exposure
            self.total_exposure += exposure
    
    def _check_circuit_breaker(self) -> None:
        """Check if circuit breaker should be triggered."""
        # Daily drawdown check
        if abs(self.daily_pnl) > self.risk_limits.max_daily_drawdown * self.total_exposure:
            if not self.circuit_breaker_triggered:
                self.circuit_breaker_triggered = True
                self._generate_alert(
                    "circuit_breaker",
                    "critical",
                    "Circuit breaker triggered - Daily drawdown limit exceeded",
                    f"Daily P&L: ${self.daily_pnl:.2f}",
                    {"daily_pnl": self.daily_pnl, "limit": self.risk_limits.max_daily_drawdown}
                )
        
        # Rapid loss check
        recent_pnl = list(self.pnl_history)[-10:] if len(self.pnl_history) >= 10 else list(self.pnl_history)
        if recent_pnl and len(recent_pnl) >= 5:
            recent_loss = sum(pnl for pnl in recent_pnl if pnl < 0)
            if abs(recent_loss) > self.config.circuit_breaker_threshold * self.total_exposure:
                if not self.circuit_breaker_triggered:
                    self.circuit_breaker_triggered = True
                    self._generate_alert(
                        "rapid_loss",
                        "critical",
                        "Circuit breaker triggered - Rapid loss detected",
                        f"Recent losses: ${recent_loss:.2f}",
                        {"recent_loss": recent_loss, "threshold": self.config.circuit_breaker_threshold}
                    )
    
    def _check_daily_reset(self) -> None:
        """Check if daily P&L should be reset."""
        current_time = time.time()
        if current_time - self.daily_start_time > 86400:  # 24 hours
            self.daily_pnl = 0
            self.daily_start_time = current_time
            self.circuit_breaker_triggered = False  # Reset circuit breaker daily
    
    async def _monitor_loop(self) -> None:
        """Main monitoring loop."""
        while self.running:
            try:
                await asyncio.sleep(30)  # Monitor every 30 seconds
                
                # Check daily reset
                self._check_daily_reset()
                
                # Update correlations
                self._update_correlations()
                
                # Check risk limits
                self._check_risk_limits()
                
                # Record P&L history
                self.pnl_history.append(self.daily_pnl)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Error in risk monitor loop", error=str(e))
    
    def _update_correlations(self) -> None:
        """Update correlation matrix."""
        # Simplified correlation update
        # In practice, this would use more sophisticated correlation calculation
        pass
    
    def _check_risk_limits(self) -> None:
        """Check all risk limits and generate alerts if needed."""
        # Total exposure check
        if self.total_exposure > self.risk_limits.max_total_exposure * 0.9:
            self._generate_alert(
                "high_exposure",
                "warning",
                "High total exposure",
                f"Current: ${self.total_exposure:.2f}, Limit: ${self.risk_limits.max_total_exposure:.2f}",
                {"current_exposure": self.total_exposure, "limit": self.risk_limits.max_total_exposure}
            )
        
        # Exchange exposure checks
        for exchange, exposure in self.exchange_exposure.items():
            if exposure > self.risk_limits.max_exchange_exposure * 0.9:
                self._generate_alert(
                    "high_exchange_exposure",
                    "warning",
                    f"High exposure on {exchange}",
                    f"Current: ${exposure:.2f}, Limit: ${self.risk_limits.max_exchange_exposure:.2f}",
                    {"exchange": exchange, "current_exposure": exposure, "limit": self.risk_limits.max_exchange_exposure}
                )
    
    def _generate_alert(self, alert_type: str, severity: str, title: str, message: str, data: Dict) -> None:
        """Generate and send alert."""
        alert = Alert(
            id=f"{alert_type}_{int(time.time())}",
            type=alert_type,
            severity=severity,
            title=title,
            message=message,
            timestamp=time.time(),
            data=data
        )
        
        self.alerts_generated += 1
        
        # Notify handlers
        for handler in self.alert_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    asyncio.create_task(handler(alert))
                else:
                    handler(alert)
            except Exception as e:
                self.logger.error("Error in alert handler", error=str(e))
    
    def get_risk_metrics(self) -> RiskMetrics:
        """Get current risk metrics."""
        # Simplified risk metrics calculation
        return RiskMetrics(
            var_1d=self.total_exposure * 0.02,  # 2% VaR
            var_1w=self.total_exposure * 0.05,  # 5% VaR
            max_drawdown=abs(min(self.pnl_history)) if self.pnl_history else 0,
            sharpe_ratio=1.5,  # Simplified
            correlation_risk=0.3,  # Simplified
            liquidity_risk=0.2,  # Simplified
            execution_risk=0.25,  # Simplified
            overall_risk_score=0.4,  # Simplified
            timestamp=time.time()
        )
    
    def get_statistics(self) -> Dict:
        """Get risk manager statistics."""
        return {
            "risk_assessments": self.risk_assessments_count,
            "opportunities_blocked": self.opportunities_blocked,
            "alerts_generated": self.alerts_generated,
            "active_positions": len(self.positions),
            "total_exposure": self.total_exposure,
            "daily_pnl": self.daily_pnl,
            "total_pnl": self.total_pnl,
            "emergency_stop": self.emergency_stop,
            "circuit_breaker": self.circuit_breaker_triggered,
            "exchange_exposures": dict(self.exchange_exposure)
        }
