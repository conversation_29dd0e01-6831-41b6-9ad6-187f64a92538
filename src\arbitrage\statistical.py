"""
Statistical arbitrage detection for ArbitrageVision.

This module implements statistical arbitrage detection using mean reversion
and cointegration analysis between trading pairs.
"""

import asyncio
import time
import uuid
from typing import Dict, List, Optional, Callable, Tuple
from collections import defaultdict, deque
import numpy as np
from scipy import stats
import structlog

from src.config.settings import Settings
from src.data.models import ArbitrageOpportunity, ArbitrageType, OrderBookSnapshot, StatisticalPair


class StatisticalArbitrageDetector:
    """Statistical arbitrage opportunity detector."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.config = settings.statistical_arbitrage
        self.logger = structlog.get_logger("StatisticalArbitrageDetector")
        
        # Price history for statistical analysis
        self.price_history: Dict[str, Dict[str, deque]] = defaultdict(lambda: defaultdict(lambda: deque(maxlen=self.config.lookback_period)))
        
        # Current order books
        self.order_books: Dict[str, Dict[str, OrderBookSnapshot]] = defaultdict(dict)
        
        # Statistical pairs tracking
        self.statistical_pairs: Dict[str, StatisticalPair] = {}
        
        # Opportunity handlers
        self.opportunity_handlers: List[Callable[[ArbitrageOpportunity], None]] = []
        
        # Performance tracking
        self.opportunities_found = 0
        self.pairs_analyzed = 0
        self.last_analysis_time = 0
        
        # Running state
        self.running = False
        self.analysis_task: Optional[asyncio.Task] = None
        
        # Analysis parameters
        self.min_observations = 100  # Minimum price observations for analysis
        self.analysis_interval = 60  # Analyze every 60 seconds
        
    async def start(self) -> None:
        """Start the statistical arbitrage detector."""
        self.logger.info("Starting statistical arbitrage detector")
        
        self.running = True
        
        # Start analysis task
        self.analysis_task = asyncio.create_task(self._analysis_loop())
        
        self.logger.info("Statistical arbitrage detector started")
    
    async def stop(self) -> None:
        """Stop the statistical arbitrage detector."""
        self.logger.info("Stopping statistical arbitrage detector")
        
        self.running = False
        
        if self.analysis_task:
            self.analysis_task.cancel()
            try:
                await self.analysis_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Statistical arbitrage detector stopped")
    
    def is_running(self) -> bool:
        """Check if detector is running."""
        return self.running
    
    def add_opportunity_handler(self, handler: Callable[[ArbitrageOpportunity], None]) -> None:
        """Add opportunity handler."""
        self.opportunity_handlers.append(handler)
    
    def remove_opportunity_handler(self, handler: Callable[[ArbitrageOpportunity], None]) -> None:
        """Remove opportunity handler."""
        if handler in self.opportunity_handlers:
            self.opportunity_handlers.remove(handler)
    
    async def process_update(self, order_book: OrderBookSnapshot) -> None:
        """Process order book update."""
        try:
            # Store order book
            self.order_books[order_book.exchange][order_book.symbol] = order_book
            
            # Update price history
            if order_book.mid_price:
                key = f"{order_book.exchange}:{order_book.symbol}"
                self.price_history[key]["price"].append(order_book.mid_price)
                self.price_history[key]["timestamp"].append(order_book.timestamp)
            
        except Exception as e:
            self.logger.error("Error processing order book update", 
                            exchange=order_book.exchange,
                            symbol=order_book.symbol,
                            error=str(e))
    
    async def _analysis_loop(self) -> None:
        """Main analysis loop."""
        while self.running:
            try:
                start_time = time.perf_counter()
                
                # Analyze all configured pairs
                for pair_config in self.config.pairs:
                    if len(pair_config) == 2:
                        await self._analyze_pair(pair_config[0], pair_config[1])
                
                # Check for opportunities in existing pairs
                await self._check_statistical_opportunities()
                
                # Track performance
                analysis_time = (time.perf_counter() - start_time) * 1000
                self.last_analysis_time = analysis_time
                
                if analysis_time > 1000:  # Log if analysis takes more than 1 second
                    self.logger.warning("Slow statistical analysis", analysis_time_ms=analysis_time)
                
                # Wait for next analysis
                await asyncio.sleep(self.analysis_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Error in analysis loop", error=str(e))
                await asyncio.sleep(10)  # Wait longer on error
    
    async def _analyze_pair(self, symbol1: str, symbol2: str) -> None:
        """Analyze a statistical arbitrage pair."""
        try:
            self.pairs_analyzed += 1
            
            # Find exchanges that have both symbols
            common_exchanges = []
            for exchange in self.order_books.keys():
                if symbol1 in self.order_books[exchange] and symbol2 in self.order_books[exchange]:
                    common_exchanges.append(exchange)
            
            if not common_exchanges:
                return
            
            # Analyze each exchange
            for exchange in common_exchanges:
                await self._analyze_pair_on_exchange(exchange, symbol1, symbol2)
                
        except Exception as e:
            self.logger.error("Error analyzing pair", symbol1=symbol1, symbol2=symbol2, error=str(e))
    
    async def _analyze_pair_on_exchange(self, exchange: str, symbol1: str, symbol2: str) -> None:
        """Analyze a pair on a specific exchange."""
        try:
            key1 = f"{exchange}:{symbol1}"
            key2 = f"{exchange}:{symbol2}"
            
            # Check if we have enough data
            if (len(self.price_history[key1]["price"]) < self.min_observations or
                len(self.price_history[key2]["price"]) < self.min_observations):
                return
            
            # Get price series
            prices1 = np.array(list(self.price_history[key1]["price"]))
            prices2 = np.array(list(self.price_history[key2]["price"]))
            
            # Ensure same length
            min_length = min(len(prices1), len(prices2))
            prices1 = prices1[-min_length:]
            prices2 = prices2[-min_length:]
            
            # Calculate statistical metrics
            correlation = np.corrcoef(prices1, prices2)[0, 1]
            
            # Calculate spread
            spread = prices1 - prices2
            mean_spread = np.mean(spread)
            std_spread = np.std(spread)
            current_spread = spread[-1]
            z_score = (current_spread - mean_spread) / std_spread if std_spread > 0 else 0
            
            # Cointegration test (simplified)
            cointegration_score = self._cointegration_test(prices1, prices2)
            
            # Half-life calculation
            half_life = self._calculate_half_life(spread)
            
            # Create or update statistical pair
            pair_key = f"{exchange}:{symbol1}:{symbol2}"
            self.statistical_pairs[pair_key] = StatisticalPair(
                symbol1=symbol1,
                symbol2=symbol2,
                exchange1=exchange,
                exchange2=exchange,
                correlation=correlation,
                cointegration_score=cointegration_score,
                half_life=half_life,
                current_spread=current_spread,
                mean_spread=mean_spread,
                std_spread=std_spread,
                z_score=z_score,
                timestamp=time.time()
            )
            
        except Exception as e:
            self.logger.error("Error analyzing pair on exchange", 
                            exchange=exchange, 
                            symbol1=symbol1, 
                            symbol2=symbol2, 
                            error=str(e))
    
    def _cointegration_test(self, prices1: np.ndarray, prices2: np.ndarray) -> float:
        """Perform cointegration test (simplified Engle-Granger test)."""
        try:
            # Simple linear regression
            slope, intercept, r_value, p_value, std_err = stats.linregress(prices1, prices2)
            
            # Calculate residuals
            residuals = prices2 - (slope * prices1 + intercept)
            
            # Augmented Dickey-Fuller test on residuals (simplified)
            # In practice, you would use statsmodels.tsa.stattools.adfuller
            # For now, return p-value approximation based on residual properties
            residual_std = np.std(residuals)
            residual_mean = np.mean(residuals)
            
            # Simple approximation - lower values indicate cointegration
            return abs(residual_mean) / residual_std if residual_std > 0 else 1.0
            
        except Exception:
            return 1.0  # No cointegration
    
    def _calculate_half_life(self, spread: np.ndarray) -> float:
        """Calculate half-life of mean reversion."""
        try:
            # Calculate lagged spread
            spread_lag = spread[:-1]
            spread_diff = np.diff(spread)
            
            # Linear regression: spread_diff = alpha + beta * spread_lag
            slope, intercept, r_value, p_value, std_err = stats.linregress(spread_lag, spread_diff)
            
            # Half-life calculation
            if slope < 0:
                half_life = -np.log(2) / slope
                return max(1, min(half_life, 1000))  # Cap between 1 and 1000
            else:
                return 1000  # No mean reversion
                
        except Exception:
            return 1000  # Default to high value
    
    async def _check_statistical_opportunities(self) -> None:
        """Check for statistical arbitrage opportunities."""
        try:
            current_time = time.time()
            
            for pair_key, pair in self.statistical_pairs.items():
                # Check if pair is suitable for trading
                if not self._is_pair_tradeable(pair):
                    continue
                
                # Check for entry signals
                if abs(pair.z_score) >= self.config.entry_z_score:
                    opportunity = await self._create_statistical_opportunity(pair)
                    if opportunity:
                        await self._handle_opportunity(opportunity)
                        
        except Exception as e:
            self.logger.error("Error checking statistical opportunities", error=str(e))
    
    def _is_pair_tradeable(self, pair: StatisticalPair) -> bool:
        """Check if a statistical pair is suitable for trading."""
        # Check correlation threshold
        if abs(pair.correlation) < self.config.correlation_threshold:
            return False
        
        # Check cointegration
        if not pair.is_cointegrated:
            return False
        
        # Check half-life
        if pair.half_life > self.config.half_life_threshold:
            return False
        
        # Check data freshness
        if time.time() - pair.timestamp > 300:  # 5 minutes
            return False
        
        return True
    
    async def _create_statistical_opportunity(self, pair: StatisticalPair) -> Optional[ArbitrageOpportunity]:
        """Create statistical arbitrage opportunity."""
        try:
            # Get current order books
            book1 = self.order_books.get(pair.exchange1, {}).get(pair.symbol1)
            book2 = self.order_books.get(pair.exchange2, {}).get(pair.symbol2)
            
            if not (book1 and book2 and book1.best_bid and book1.best_ask and book2.best_bid and book2.best_ask):
                return None
            
            # Determine trade direction based on z-score
            if pair.z_score > self.config.entry_z_score:
                # Spread is too high - sell symbol1, buy symbol2
                buy_exchange = pair.exchange2
                sell_exchange = pair.exchange1
                buy_symbol = pair.symbol2
                sell_symbol = pair.symbol1
                buy_price = book2.best_ask.price
                sell_price = book1.best_bid.price
            elif pair.z_score < -self.config.entry_z_score:
                # Spread is too low - buy symbol1, sell symbol2
                buy_exchange = pair.exchange1
                sell_exchange = pair.exchange2
                buy_symbol = pair.symbol1
                sell_symbol = pair.symbol2
                buy_price = book1.best_ask.price
                sell_price = book2.best_bid.price
            else:
                return None
            
            # Calculate position size
            max_position_value = self.config.max_position_size
            quantity = min(
                max_position_value / buy_price,
                book1.best_ask.quantity if buy_symbol == pair.symbol1 else book1.best_bid.quantity,
                book2.best_ask.quantity if buy_symbol == pair.symbol2 else book2.best_bid.quantity
            )
            
            if quantity <= 0:
                return None
            
            # Estimate profit (simplified)
            expected_reversion = pair.std_spread * (abs(pair.z_score) - self.config.exit_z_score)
            profit_absolute = expected_reversion * quantity
            profit_percentage = (profit_absolute / (buy_price * quantity)) * 100
            
            # Check minimum profit threshold
            if profit_percentage < self.config.min_profit_threshold * 100:
                return None
            
            opportunity = ArbitrageOpportunity(
                id=str(uuid.uuid4()),
                type=ArbitrageType.STATISTICAL,
                symbol=f"{pair.symbol1}+{pair.symbol2}",  # Pair notation
                buy_exchange=buy_exchange,
                sell_exchange=sell_exchange,
                buy_price=buy_price,
                sell_price=sell_price,
                quantity=quantity,
                profit_absolute=profit_absolute,
                profit_percentage=profit_percentage,
                timestamp=time.time(),
                confidence=self._calculate_statistical_confidence(pair),
                metadata={
                    "pair_symbol1": pair.symbol1,
                    "pair_symbol2": pair.symbol2,
                    "z_score": pair.z_score,
                    "correlation": pair.correlation,
                    "half_life": pair.half_life,
                    "signal_strength": pair.signal_strength,
                    "entry_z_score": self.config.entry_z_score,
                    "exit_z_score": self.config.exit_z_score,
                    "arbitrage_type": "statistical"
                }
            )
            
            return opportunity
            
        except Exception as e:
            self.logger.error("Error creating statistical opportunity", error=str(e))
            return None
    
    def _calculate_statistical_confidence(self, pair: StatisticalPair) -> float:
        """Calculate confidence for statistical arbitrage."""
        confidence_factors = []
        
        # Correlation strength
        correlation_confidence = abs(pair.correlation)
        confidence_factors.append(correlation_confidence)
        
        # Cointegration strength
        cointegration_confidence = max(0, 1 - pair.cointegration_score)
        confidence_factors.append(cointegration_confidence)
        
        # Signal strength
        signal_strength_map = {"very_strong": 1.0, "strong": 0.8, "moderate": 0.6, "weak": 0.4}
        signal_confidence = signal_strength_map.get(pair.signal_strength, 0.5)
        confidence_factors.append(signal_confidence)
        
        # Half-life (shorter is better)
        half_life_confidence = max(0, 1 - (pair.half_life / self.config.half_life_threshold))
        confidence_factors.append(half_life_confidence)
        
        return sum(confidence_factors) / len(confidence_factors) if confidence_factors else 0.5
    
    async def _handle_opportunity(self, opportunity: ArbitrageOpportunity) -> None:
        """Handle discovered statistical arbitrage opportunity."""
        try:
            # Track statistics
            self.opportunities_found += 1
            
            # Log opportunity
            self.logger.info("Statistical arbitrage opportunity found",
                           symbol=opportunity.symbol,
                           buy_exchange=opportunity.buy_exchange,
                           sell_exchange=opportunity.sell_exchange,
                           profit_percentage=opportunity.profit_percentage,
                           confidence=opportunity.confidence,
                           z_score=opportunity.metadata.get("z_score"),
                           signal_strength=opportunity.metadata.get("signal_strength"))
            
            # Notify handlers
            for handler in self.opportunity_handlers:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(opportunity)
                    else:
                        handler(opportunity)
                except Exception as e:
                    self.logger.error("Error in opportunity handler", error=str(e))
                    
        except Exception as e:
            self.logger.error("Error handling statistical opportunity", error=str(e))
    
    def get_statistics(self) -> Dict:
        """Get detector statistics."""
        return {
            "opportunities_found": self.opportunities_found,
            "pairs_analyzed": self.pairs_analyzed,
            "last_analysis_time_ms": self.last_analysis_time,
            "active_pairs": len(self.statistical_pairs),
            "tradeable_pairs": len([p for p in self.statistical_pairs.values() if self._is_pair_tradeable(p)]),
            "price_series_count": sum(len(series["price"]) for series in self.price_history.values())
        }
