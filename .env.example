# ArbitrageVision Environment Configuration
# Copy this file to .env and update with your values

# Application Settings
DEBUG=false
LOG_LEVEL=INFO
HOST=0.0.0.0
PORT=8000
WORKERS=4

# Database Configuration
REDIS_URL=redis://localhost:6379
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=arbitrage-token-123456789
INFLUXDB_ORG=arbitrage
INFLUXDB_BUCKET=market_data

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092

# Exchange API Keys (Binance)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_API_SECRET=your_binance_api_secret_here

# Exchange API Keys (Coinbase Pro)
COINBASE_API_KEY=your_coinbase_api_key_here
COINBASE_API_SECRET=your_coinbase_api_secret_here
COINBASE_PASSPHRASE=your_coinbase_passphrase_here

# Exchange API Keys (Kraken)
KRAKEN_API_KEY=your_kraken_api_key_here
KRAKEN_API_SECRET=your_kraken_api_secret_here

# Exchange API Keys (KuCoin)
KUCOIN_API_KEY=your_kucoin_api_key_here
KUCOIN_API_SECRET=your_kucoin_api_secret_here
KUCOIN_PASSPHRASE=your_kucoin_passphrase_here

# Alert Configuration
WEBHOOK_URL=https://your-webhook-url.com/alerts
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# Email Configuration
SMTP_SERVER=smtp.gmail.com
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password

# Security
JWT_SECRET_KEY=your_jwt_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here

# Monitoring
ALERT_WEBHOOK_URL=https://your-monitoring-webhook.com/alerts
