"""
Order book management for ArbitrageVision.

This module provides high-performance order book management with microsecond-level
updates and efficient data structures.
"""

import asyncio
import time
from typing import Dict, List, Optional, Callable, Set
from collections import defaultdict, deque
import heapq
import structlog
from dataclasses import dataclass

from src.data.models import OrderB<PERSON><PERSON>napshot, PriceLevel
from src.exchanges.base import OrderBook, OrderBookLevel


@dataclass
class OrderBookStats:
    """Order book statistics."""
    symbol: str
    exchange: str
    update_count: int
    last_update: float
    avg_spread: float
    avg_depth: float
    quality_score: float


class OrderBookManager:
    """High-performance order book manager."""
    
    def __init__(self, max_depth: int = 50, max_history: int = 1000):
        self.logger = structlog.get_logger("OrderBookManager")
        self.max_depth = max_depth
        self.max_history = max_history
        
        # Order book storage
        self.order_books: Dict[str, Dict[str, OrderBookSnapshot]] = defaultdict(dict)
        
        # Order book history for analysis
        self.order_book_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history))
        
        # Statistics tracking
        self.stats: Dict[str, OrderBookStats] = {}
        
        # Subscribers for order book updates
        self.subscribers: List[Callable[[OrderBookSnapshot], None]] = []
        
        # Quality monitoring
        self.quality_thresholds = {
            "max_spread_percentage": 5.0,  # 5%
            "min_depth": 1000,  # $1000
            "max_staleness": 5.0,  # 5 seconds
        }
        
        # Performance tracking
        self.update_times: deque = deque(maxlen=1000)
        self.processing_times: deque = deque(maxlen=1000)
        
    def add_subscriber(self, callback: Callable[[OrderBookSnapshot], None]) -> None:
        """Add subscriber for order book updates."""
        self.subscribers.append(callback)
    
    def remove_subscriber(self, callback: Callable[[OrderBookSnapshot], None]) -> None:
        """Remove subscriber for order book updates."""
        if callback in self.subscribers:
            self.subscribers.remove(callback)
    
    async def update_order_book(self, order_book: OrderBook) -> None:
        """Update order book with new data."""
        start_time = time.perf_counter()
        
        try:
            # Convert to internal format
            snapshot = self._convert_to_snapshot(order_book)
            
            # Validate order book quality
            if not self._validate_order_book(snapshot):
                self.logger.warning("Order book failed quality check", 
                                  symbol=snapshot.symbol, 
                                  exchange=snapshot.exchange)
                return
            
            # Store order book
            key = f"{snapshot.exchange}:{snapshot.symbol}"
            self.order_books[snapshot.exchange][snapshot.symbol] = snapshot
            
            # Add to history
            self.order_book_history[key].append(snapshot)
            
            # Update statistics
            self._update_stats(snapshot)
            
            # Notify subscribers
            await self._notify_subscribers(snapshot)
            
            # Track performance
            processing_time = (time.perf_counter() - start_time) * 1000  # Convert to ms
            self.processing_times.append(processing_time)
            self.update_times.append(time.time())
            
            if processing_time > 1.0:  # Log if processing takes more than 1ms
                self.logger.warning("Slow order book update", 
                                  processing_time_ms=processing_time,
                                  symbol=snapshot.symbol,
                                  exchange=snapshot.exchange)
            
        except Exception as e:
            self.logger.error("Error updating order book", 
                            symbol=order_book.symbol,
                            exchange=order_book.exchange,
                            error=str(e))
    
    def get_order_book(self, exchange: str, symbol: str) -> Optional[OrderBookSnapshot]:
        """Get current order book for exchange and symbol."""
        return self.order_books.get(exchange, {}).get(symbol)
    
    def get_all_order_books(self, symbol: str) -> Dict[str, OrderBookSnapshot]:
        """Get order books for a symbol across all exchanges."""
        result = {}
        for exchange, books in self.order_books.items():
            if symbol in books:
                result[exchange] = books[symbol]
        return result
    
    def get_best_prices(self, symbol: str) -> Dict[str, Dict[str, float]]:
        """Get best bid/ask prices across all exchanges for a symbol."""
        result = {}
        for exchange, order_book in self.get_all_order_books(symbol).items():
            if order_book.best_bid and order_book.best_ask:
                result[exchange] = {
                    "bid": order_book.best_bid.price,
                    "ask": order_book.best_ask.price,
                    "spread": order_book.spread,
                    "timestamp": order_book.timestamp
                }
        return result
    
    def get_arbitrage_opportunities(self, symbol: str, min_profit: float = 0.001) -> List[Dict]:
        """Find simple arbitrage opportunities for a symbol."""
        opportunities = []
        prices = self.get_best_prices(symbol)
        
        exchanges = list(prices.keys())
        for i, buy_exchange in enumerate(exchanges):
            for j, sell_exchange in enumerate(exchanges):
                if i >= j:
                    continue
                
                buy_price = prices[buy_exchange]["ask"]
                sell_price = prices[sell_exchange]["bid"]
                
                if sell_price > buy_price:
                    profit_abs = sell_price - buy_price
                    profit_pct = (profit_abs / buy_price) * 100
                    
                    if profit_pct >= min_profit * 100:
                        opportunities.append({
                            "symbol": symbol,
                            "buy_exchange": buy_exchange,
                            "sell_exchange": sell_exchange,
                            "buy_price": buy_price,
                            "sell_price": sell_price,
                            "profit_absolute": profit_abs,
                            "profit_percentage": profit_pct,
                            "timestamp": min(prices[buy_exchange]["timestamp"],
                                           prices[sell_exchange]["timestamp"])
                        })
        
        return sorted(opportunities, key=lambda x: x["profit_percentage"], reverse=True)
    
    def get_order_book_history(self, exchange: str, symbol: str, limit: int = 100) -> List[OrderBookSnapshot]:
        """Get order book history for analysis."""
        key = f"{exchange}:{symbol}"
        history = list(self.order_book_history.get(key, []))
        return history[-limit:] if limit else history
    
    def get_statistics(self, exchange: str = None, symbol: str = None) -> Dict:
        """Get order book statistics."""
        if exchange and symbol:
            key = f"{exchange}:{symbol}"
            return self.stats.get(key, {})
        
        # Return aggregated statistics
        total_updates = sum(stat.update_count for stat in self.stats.values())
        avg_processing_time = sum(self.processing_times) / len(self.processing_times) if self.processing_times else 0
        
        return {
            "total_order_books": len(self.stats),
            "total_updates": total_updates,
            "avg_processing_time_ms": avg_processing_time,
            "updates_per_second": len([t for t in self.update_times if time.time() - t < 1.0]),
            "quality_issues": sum(1 for stat in self.stats.values() if stat.quality_score < 0.8)
        }
    
    def cleanup_stale_data(self, max_age: float = 30.0) -> None:
        """Clean up stale order book data."""
        current_time = time.time()
        stale_keys = []
        
        for exchange, books in self.order_books.items():
            for symbol, order_book in books.items():
                if current_time - order_book.timestamp > max_age:
                    stale_keys.append((exchange, symbol))
        
        for exchange, symbol in stale_keys:
            del self.order_books[exchange][symbol]
            key = f"{exchange}:{symbol}"
            if key in self.stats:
                del self.stats[key]
            
            self.logger.info("Removed stale order book", 
                           exchange=exchange, 
                           symbol=symbol)
    
    def _convert_to_snapshot(self, order_book: OrderBook) -> OrderBookSnapshot:
        """Convert OrderBook to OrderBookSnapshot."""
        bids = [
            PriceLevel(
                price=level.price,
                quantity=level.quantity,
                timestamp=level.timestamp
            )
            for level in order_book.bids[:self.max_depth]
        ]
        
        asks = [
            PriceLevel(
                price=level.price,
                quantity=level.quantity,
                timestamp=level.timestamp
            )
            for level in order_book.asks[:self.max_depth]
        ]
        
        return OrderBookSnapshot(
            symbol=order_book.symbol,
            exchange=order_book.exchange,
            bids=bids,
            asks=asks,
            timestamp=order_book.timestamp,
            sequence=order_book.sequence
        )
    
    def _validate_order_book(self, snapshot: OrderBookSnapshot) -> bool:
        """Validate order book quality."""
        try:
            # Check if we have both bids and asks
            if not snapshot.bids or not snapshot.asks:
                return False
            
            # Check spread
            if snapshot.spread_percentage and snapshot.spread_percentage > self.quality_thresholds["max_spread_percentage"]:
                return False
            
            # Check depth
            bid_depth = sum(level.quantity * level.price for level in snapshot.bids[:5])
            ask_depth = sum(level.quantity * level.price for level in snapshot.asks[:5])
            
            if min(bid_depth, ask_depth) < self.quality_thresholds["min_depth"]:
                return False
            
            # Check staleness
            if time.time() - snapshot.timestamp > self.quality_thresholds["max_staleness"]:
                return False
            
            # Check price ordering
            for i in range(1, len(snapshot.bids)):
                if snapshot.bids[i].price >= snapshot.bids[i-1].price:
                    return False
            
            for i in range(1, len(snapshot.asks)):
                if snapshot.asks[i].price <= snapshot.asks[i-1].price:
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error("Error validating order book", error=str(e))
            return False
    
    def _update_stats(self, snapshot: OrderBookSnapshot) -> None:
        """Update order book statistics."""
        key = f"{snapshot.exchange}:{snapshot.symbol}"
        
        if key not in self.stats:
            self.stats[key] = OrderBookStats(
                symbol=snapshot.symbol,
                exchange=snapshot.exchange,
                update_count=0,
                last_update=0,
                avg_spread=0,
                avg_depth=0,
                quality_score=1.0
            )
        
        stats = self.stats[key]
        stats.update_count += 1
        stats.last_update = snapshot.timestamp
        
        # Update moving averages
        if snapshot.spread_percentage:
            stats.avg_spread = (stats.avg_spread * 0.9) + (snapshot.spread_percentage * 0.1)
        
        # Calculate depth
        depth = sum(level.quantity * level.price for level in snapshot.bids[:5])
        stats.avg_depth = (stats.avg_depth * 0.9) + (depth * 0.1)
        
        # Update quality score
        quality_factors = []
        
        # Spread quality (lower is better)
        if snapshot.spread_percentage:
            spread_quality = max(0, 1 - (snapshot.spread_percentage / 5.0))
            quality_factors.append(spread_quality)
        
        # Depth quality
        depth_quality = min(1.0, depth / 10000)  # $10k as reference
        quality_factors.append(depth_quality)
        
        # Freshness quality
        age = time.time() - snapshot.timestamp
        freshness_quality = max(0, 1 - (age / 5.0))  # 5 seconds as reference
        quality_factors.append(freshness_quality)
        
        stats.quality_score = sum(quality_factors) / len(quality_factors) if quality_factors else 0
    
    async def _notify_subscribers(self, snapshot: OrderBookSnapshot) -> None:
        """Notify all subscribers of order book update."""
        for callback in self.subscribers:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(snapshot)
                else:
                    callback(snapshot)
            except Exception as e:
                self.logger.error("Error in order book subscriber callback", error=str(e))
