"""
Configuration settings for ArbitrageVision application.

This module handles all configuration loading and validation using Pydantic.
"""

import os
from functools import lru_cache
from typing import Dict, List, Optional, Any
from pathlib import Path

import yaml
from pydantic import BaseSettings, Field, validator
from pydantic_settings import BaseSettings as PydanticBaseSettings


class ExchangeConfig(BaseSettings):
    """Exchange configuration model."""
    name: str
    enabled: bool = True
    api_key: str = ""
    api_secret: str = ""
    passphrase: Optional[str] = None
    testnet: bool = True
    sandbox: bool = True
    base_url: str
    ws_url: str
    rate_limits: Dict[str, int] = {}
    fees: Dict[str, float] = {}
    supported_pairs: List[str] = []


class ArbitrageConfig(BaseSettings):
    """Arbitrage detection configuration."""
    enabled: bool = True
    min_profit_threshold: float = 0.001
    max_profit_threshold: float = 0.1
    min_volume_threshold: float = 1000
    max_position_size: float = 10000
    trading_pairs: List[str] = []


class SimpleArbitrageConfig(ArbitrageConfig):
    """Simple arbitrage configuration."""
    price_tolerance: float = 0.0001
    update_frequency: int = 100
    max_spread_age: int = 1000
    min_liquidity_depth: int = 5


class TriangularArbitrageConfig(ArbitrageConfig):
    """Triangular arbitrage configuration."""
    max_path_length: int = 3
    path_timeout: int = 500
    triangles: List[List[str]] = []


class StatisticalArbitrageConfig(ArbitrageConfig):
    """Statistical arbitrage configuration."""
    lookback_period: int = 3600
    z_score_threshold: float = 2.0
    correlation_threshold: float = 0.8
    half_life_threshold: int = 300
    entry_z_score: float = 2.0
    exit_z_score: float = 0.5
    stop_loss_z_score: float = 3.0
    pairs: List[List[str]] = []


class RiskManagementConfig(BaseSettings):
    """Risk management configuration."""
    max_total_exposure: float = 100000
    max_exchange_exposure: float = 25000
    max_pair_exposure: float = 10000
    max_correlation_exposure: float = 0.7
    correlation_window: int = 86400
    max_daily_drawdown: float = 0.05
    max_total_drawdown: float = 0.15
    emergency_stop_loss: float = 0.1
    circuit_breaker_threshold: float = 0.02
    max_latency_threshold: int = 1000
    latency_window: int = 300


class AlertConfig(BaseSettings):
    """Alert configuration."""
    high_profit_threshold: float = 0.01
    medium_profit_threshold: float = 0.005
    low_profit_threshold: float = 0.002
    high_risk_threshold: float = 0.8
    medium_risk_threshold: float = 0.6
    connection_failure_alert: bool = True
    latency_spike_alert: bool = True
    data_quality_alert: bool = True
    channels: List[Dict[str, Any]] = []


class PrometheusConfig(BaseSettings):
    """Prometheus configuration."""
    enabled: bool = True
    port: int = 8001
    path: str = "/metrics"


class MonitoringConfig(BaseSettings):
    """Monitoring configuration."""
    prometheus: PrometheusConfig = PrometheusConfig()


class DatabaseConfig(BaseSettings):
    """Database configuration."""
    redis_url: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    influxdb_url: str = Field(default="http://localhost:8086", env="INFLUXDB_URL")
    influxdb_token: str = Field(default="", env="INFLUXDB_TOKEN")
    influxdb_org: str = Field(default="arbitrage", env="INFLUXDB_ORG")
    influxdb_bucket: str = Field(default="market_data", env="INFLUXDB_BUCKET")


class KafkaConfig(BaseSettings):
    """Kafka configuration."""
    bootstrap_servers: str = Field(default="localhost:9092", env="KAFKA_BOOTSTRAP_SERVERS")
    topics: Dict[str, str] = {
        "market_data": "market_data",
        "arbitrage_opportunities": "arbitrage_opportunities",
        "alerts": "alerts",
        "risk_events": "risk_events"
    }


class Settings(PydanticBaseSettings):
    """Main application settings."""
    
    # Application settings
    app_name: str = "ArbitrageVision"
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Server settings
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    workers: int = Field(default=4, env="WORKERS")
    
    # Configuration file paths
    config_dir: Path = Field(default=Path("config"))
    exchanges_config_file: Path = Field(default=Path("config/exchanges.yaml"))
    arbitrage_config_file: Path = Field(default=Path("config/arbitrage.yaml"))
    monitoring_config_file: Path = Field(default=Path("config/monitoring.yaml"))
    
    # Database configurations
    database: DatabaseConfig = DatabaseConfig()
    kafka: KafkaConfig = KafkaConfig()
    
    # Component configurations (loaded from files)
    exchanges: Dict[str, ExchangeConfig] = {}
    simple_arbitrage: SimpleArbitrageConfig = SimpleArbitrageConfig()
    triangular_arbitrage: TriangularArbitrageConfig = TriangularArbitrageConfig()
    statistical_arbitrage: StatisticalArbitrageConfig = StatisticalArbitrageConfig()
    risk_management: RiskManagementConfig = RiskManagementConfig()
    alerts: AlertConfig = AlertConfig()
    monitoring: MonitoringConfig = MonitoringConfig()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._load_config_files()
    
    def _load_config_files(self):
        """Load configuration from YAML files."""
        try:
            # Load exchanges configuration
            if self.exchanges_config_file.exists():
                with open(self.exchanges_config_file, 'r') as f:
                    exchanges_data = yaml.safe_load(f)
                    if 'exchanges' in exchanges_data:
                        for name, config in exchanges_data['exchanges'].items():
                            # Expand environment variables
                            config = self._expand_env_vars(config)
                            self.exchanges[name] = ExchangeConfig(**config)
            
            # Load arbitrage configuration
            if self.arbitrage_config_file.exists():
                with open(self.arbitrage_config_file, 'r') as f:
                    arbitrage_data = yaml.safe_load(f)
                    
                    if 'simple_arbitrage' in arbitrage_data:
                        self.simple_arbitrage = SimpleArbitrageConfig(**arbitrage_data['simple_arbitrage'])
                    
                    if 'triangular_arbitrage' in arbitrage_data:
                        self.triangular_arbitrage = TriangularArbitrageConfig(**arbitrage_data['triangular_arbitrage'])
                    
                    if 'statistical_arbitrage' in arbitrage_data:
                        self.statistical_arbitrage = StatisticalArbitrageConfig(**arbitrage_data['statistical_arbitrage'])
                    
                    if 'risk_management' in arbitrage_data:
                        self.risk_management = RiskManagementConfig(**arbitrage_data['risk_management'])
                    
                    if 'alerts' in arbitrage_data:
                        self.alerts = AlertConfig(**arbitrage_data['alerts'])
            
            # Load monitoring configuration
            if self.monitoring_config_file.exists():
                with open(self.monitoring_config_file, 'r') as f:
                    monitoring_data = yaml.safe_load(f)
                    if 'prometheus' in monitoring_data:
                        self.monitoring.prometheus = PrometheusConfig(**monitoring_data['prometheus'])
        
        except Exception as e:
            print(f"Warning: Failed to load configuration files: {e}")
    
    def _expand_env_vars(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Expand environment variables in configuration values."""
        if isinstance(config, dict):
            return {k: self._expand_env_vars(v) for k, v in config.items()}
        elif isinstance(config, list):
            return [self._expand_env_vars(item) for item in config]
        elif isinstance(config, str) and config.startswith("${") and config.endswith("}"):
            env_var = config[2:-1]
            return os.getenv(env_var, config)
        else:
            return config
    
    @validator('log_level')
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'Log level must be one of {valid_levels}')
        return v.upper()
    
    def get_enabled_exchanges(self) -> Dict[str, ExchangeConfig]:
        """Get only enabled exchanges."""
        return {name: config for name, config in self.exchanges.items() if config.enabled}
    
    def get_exchange_config(self, exchange_name: str) -> Optional[ExchangeConfig]:
        """Get configuration for a specific exchange."""
        return self.exchanges.get(exchange_name)
    
    def is_exchange_enabled(self, exchange_name: str) -> bool:
        """Check if an exchange is enabled."""
        config = self.get_exchange_config(exchange_name)
        return config is not None and config.enabled


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()
