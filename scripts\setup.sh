#!/bin/bash

# ArbitrageVision Setup Script
# This script sets up the development environment for ArbitrageVision

set -e

echo "🚀 Setting up ArbitrageVision Development Environment"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python 3.9+ is installed
check_python() {
    print_status "Checking Python version..."
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        REQUIRED_VERSION="3.9"
        if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" = "$REQUIRED_VERSION" ]; then
            print_success "Python $PYTHON_VERSION found"
        else
            print_error "Python 3.9+ required, found $PYTHON_VERSION"
            exit 1
        fi
    else
        print_error "Python 3 not found. Please install Python 3.9+"
        exit 1
    fi
}

# Check if Docker is installed
check_docker() {
    print_status "Checking Docker installation..."
    if command -v docker &> /dev/null; then
        print_success "Docker found"
        if command -v docker-compose &> /dev/null; then
            print_success "Docker Compose found"
        else
            print_error "Docker Compose not found. Please install Docker Compose"
            exit 1
        fi
    else
        print_error "Docker not found. Please install Docker"
        exit 1
    fi
}

# Check if Node.js is installed
check_nodejs() {
    print_status "Checking Node.js installation..."
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node -v | cut -d'v' -f2)
        REQUIRED_VERSION="16.0.0"
        if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" = "$REQUIRED_VERSION" ]; then
            print_success "Node.js $NODE_VERSION found"
        else
            print_warning "Node.js 16+ recommended, found $NODE_VERSION"
        fi
    else
        print_warning "Node.js not found. Dashboard features will be limited"
    fi
}

# Create virtual environment
setup_python_env() {
    print_status "Setting up Python virtual environment..."
    
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        print_success "Virtual environment created"
    else
        print_warning "Virtual environment already exists"
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    print_status "Upgrading pip..."
    pip install --upgrade pip setuptools wheel
    
    # Install requirements
    print_status "Installing Python dependencies..."
    pip install -r requirements.txt
    
    print_success "Python environment setup complete"
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    directories=(
        "logs"
        "data"
        "tests/unit"
        "tests/integration"
        "tests/performance"
        "tests/e2e"
        "deployment/docker"
        "deployment/kubernetes"
        "deployment/monitoring"
        "deployment/ci-cd"
        "docs/api"
        "docs/architecture"
        "docs/deployment"
        "dashboard/public"
        "dashboard/src/components"
        "dashboard/src/pages"
        "dashboard/src/services"
        "dashboard/src/styles"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
    done
    
    print_success "Directories created"
}

# Copy configuration templates
setup_config() {
    print_status "Setting up configuration files..."
    
    # Copy environment file
    if [ ! -f ".env" ]; then
        cp .env.example .env
        print_success "Environment file created from template"
        print_warning "Please edit .env file with your configuration"
    else
        print_warning ".env file already exists"
    fi
    
    # Copy exchange configuration template
    if [ ! -f "config/exchanges.yaml" ]; then
        print_warning "Please create config/exchanges.yaml with your exchange API credentials"
        print_warning "Use config/exchanges.yaml as a template"
    fi
}

# Setup pre-commit hooks
setup_pre_commit() {
    print_status "Setting up pre-commit hooks..."
    
    if command -v pre-commit &> /dev/null; then
        pre-commit install
        print_success "Pre-commit hooks installed"
    else
        print_warning "pre-commit not found. Installing..."
        pip install pre-commit
        pre-commit install
        print_success "Pre-commit hooks installed"
    fi
}

# Setup dashboard
setup_dashboard() {
    if command -v npm &> /dev/null; then
        print_status "Setting up dashboard..."
        
        # Create package.json for dashboard
        cat > dashboard/package.json << EOF
{
  "name": "arbitrage-vision-dashboard",
  "version": "1.0.0",
  "description": "ArbitrageVision Material Design Dashboard",
  "main": "src/index.js",
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  },
  "dependencies": {
    "@material-ui/core": "^4.12.4",
    "@material-ui/icons": "^4.11.3",
    "@material-ui/lab": "^4.0.0-alpha.61",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.8.1",
    "react-scripts": "5.0.1",
    "recharts": "^2.5.0",
    "socket.io-client": "^4.6.1",
    "axios": "^1.3.4"
  },
  "devDependencies": {
    "@types/react": "^18.0.28",
    "@types/react-dom": "^18.0.11"
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  }
}
EOF
        
        print_success "Dashboard package.json created"
        print_status "Run 'cd dashboard && npm install' to install dashboard dependencies"
    else
        print_warning "npm not found. Dashboard setup skipped"
    fi
}

# Create monitoring configuration
setup_monitoring() {
    print_status "Setting up monitoring configuration..."
    
    # Create Prometheus configuration
    mkdir -p deployment/monitoring
    cat > deployment/monitoring/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'arbitrage-vision'
    static_configs:
      - targets: ['arbitrage-api:8001']
    scrape_interval: 5s
    metrics_path: /metrics

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'influxdb'
    static_configs:
      - targets: ['influxdb:8086']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
EOF
    
    print_success "Monitoring configuration created"
}

# Main setup function
main() {
    echo
    print_status "Starting ArbitrageVision setup..."
    echo
    
    # Run checks
    check_python
    check_docker
    check_nodejs
    
    echo
    print_status "Setting up project..."
    
    # Setup components
    create_directories
    setup_python_env
    setup_config
    setup_pre_commit
    setup_dashboard
    setup_monitoring
    
    echo
    print_success "🎉 ArbitrageVision setup completed successfully!"
    echo
    print_status "Next steps:"
    echo "1. Edit .env file with your configuration"
    echo "2. Add your exchange API credentials to config/exchanges.yaml"
    echo "3. Run 'docker-compose up -d' to start services"
    echo "4. Run 'python src/main.py' to start the application"
    echo "5. Visit http://localhost:8000/docs for API documentation"
    echo
    print_warning "Remember to never commit your API keys or .env file to version control!"
    echo
}

# Run main function
main "$@"
