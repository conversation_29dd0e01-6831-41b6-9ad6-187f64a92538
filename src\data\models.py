"""
Data models for ArbitrageVision.

This module defines the core data structures used throughout the application.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from enum import Enum
import time


class OrderType(Enum):
    """Order type enumeration."""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


class OrderStatus(Enum):
    """Order status enumeration."""
    PENDING = "pending"
    OPEN = "open"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"


class ArbitrageType(Enum):
    """Arbitrage type enumeration."""
    SIMPLE = "simple"
    TRIANGULAR = "triangular"
    STATISTICAL = "statistical"
    BASIS = "basis"


@dataclass
class MarketData:
    """Market data structure."""
    symbol: str
    exchange: str
    timestamp: float
    data: Dict[str, Any]
    
    def is_stale(self, max_age: float = 5.0) -> bool:
        """Check if market data is stale."""
        return time.time() - self.timestamp > max_age


@dataclass
class PriceLevel:
    """Price level in order book."""
    price: float
    quantity: float
    timestamp: float
    orders: int = 1
    
    def __post_init__(self):
        """Validate price level data."""
        if self.price <= 0:
            raise ValueError("Price must be positive")
        if self.quantity < 0:
            raise ValueError("Quantity cannot be negative")


@dataclass
class OrderBookSnapshot:
    """Order book snapshot with metadata."""
    symbol: str
    exchange: str
    bids: List[PriceLevel]
    asks: List[PriceLevel]
    timestamp: float
    sequence: Optional[int] = None
    checksum: Optional[str] = None
    
    def __post_init__(self):
        """Sort order book levels."""
        self.bids.sort(key=lambda x: x.price, reverse=True)  # Highest first
        self.asks.sort(key=lambda x: x.price)  # Lowest first
    
    @property
    def best_bid(self) -> Optional[PriceLevel]:
        """Get best bid."""
        return self.bids[0] if self.bids else None
    
    @property
    def best_ask(self) -> Optional[PriceLevel]:
        """Get best ask."""
        return self.asks[0] if self.asks else None
    
    @property
    def spread(self) -> Optional[float]:
        """Get bid-ask spread."""
        if self.best_bid and self.best_ask:
            return self.best_ask.price - self.best_bid.price
        return None
    
    @property
    def spread_percentage(self) -> Optional[float]:
        """Get spread as percentage of mid price."""
        if self.best_bid and self.best_ask:
            mid_price = (self.best_bid.price + self.best_ask.price) / 2
            spread = self.best_ask.price - self.best_bid.price
            return (spread / mid_price) * 100
        return None
    
    @property
    def mid_price(self) -> Optional[float]:
        """Get mid price."""
        if self.best_bid and self.best_ask:
            return (self.best_bid.price + self.best_ask.price) / 2
        return None
    
    def get_depth(self, side: str, max_quantity: float) -> float:
        """Get order book depth up to max quantity."""
        levels = self.bids if side == "bid" else self.asks
        total_quantity = 0
        
        for level in levels:
            if total_quantity + level.quantity >= max_quantity:
                return total_quantity + level.quantity
            total_quantity += level.quantity
        
        return total_quantity
    
    def get_volume_weighted_price(self, side: str, quantity: float) -> Optional[float]:
        """Get volume weighted average price for given quantity."""
        levels = self.bids if side == "bid" else self.asks
        remaining_quantity = quantity
        total_cost = 0
        
        for level in levels:
            if remaining_quantity <= 0:
                break
            
            level_quantity = min(level.quantity, remaining_quantity)
            total_cost += level_quantity * level.price
            remaining_quantity -= level_quantity
        
        if remaining_quantity > 0:
            return None  # Not enough liquidity
        
        return total_cost / quantity


@dataclass
class TradeData:
    """Trade data structure."""
    symbol: str
    exchange: str
    price: float
    quantity: float
    side: str
    timestamp: float
    trade_id: Optional[str] = None
    buyer_maker: Optional[bool] = None
    
    @property
    def value(self) -> float:
        """Get trade value."""
        return self.price * self.quantity


@dataclass
class ArbitrageOpportunity:
    """Arbitrage opportunity data structure."""
    id: str
    type: ArbitrageType
    symbol: str
    buy_exchange: str
    sell_exchange: str
    buy_price: float
    sell_price: float
    quantity: float
    profit_absolute: float
    profit_percentage: float
    timestamp: float
    confidence: float = 1.0
    risk_score: float = 0.0
    execution_time_estimate: float = 0.0
    
    # Additional data for different arbitrage types
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def is_profitable(self) -> bool:
        """Check if opportunity is profitable."""
        return self.profit_absolute > 0
    
    @property
    def profit_ratio(self) -> float:
        """Get profit ratio (profit / investment)."""
        return self.profit_absolute / (self.buy_price * self.quantity)
    
    def is_expired(self, max_age: float = 1.0) -> bool:
        """Check if opportunity has expired."""
        return time.time() - self.timestamp > max_age


@dataclass
class TriangularPath:
    """Triangular arbitrage path."""
    currencies: List[str]
    exchanges: List[str]
    rates: List[float]
    quantities: List[float]
    total_return: float
    profit: float
    timestamp: float
    
    @property
    def is_profitable(self) -> bool:
        """Check if triangular path is profitable."""
        return self.total_return > 1.0


@dataclass
class StatisticalPair:
    """Statistical arbitrage pair data."""
    symbol1: str
    symbol2: str
    exchange1: str
    exchange2: str
    correlation: float
    cointegration_score: float
    half_life: float
    current_spread: float
    mean_spread: float
    std_spread: float
    z_score: float
    timestamp: float
    
    @property
    def is_cointegrated(self) -> bool:
        """Check if pair is cointegrated."""
        return self.cointegration_score < 0.05  # 5% significance level
    
    @property
    def signal_strength(self) -> str:
        """Get signal strength based on z-score."""
        abs_z = abs(self.z_score)
        if abs_z > 3.0:
            return "very_strong"
        elif abs_z > 2.0:
            return "strong"
        elif abs_z > 1.0:
            return "moderate"
        else:
            return "weak"


@dataclass
class RiskMetrics:
    """Risk metrics for positions and opportunities."""
    var_1d: float  # 1-day Value at Risk
    var_1w: float  # 1-week Value at Risk
    max_drawdown: float
    sharpe_ratio: float
    correlation_risk: float
    liquidity_risk: float
    execution_risk: float
    overall_risk_score: float
    timestamp: float
    
    def get_risk_level(self) -> str:
        """Get risk level classification."""
        if self.overall_risk_score < 0.3:
            return "low"
        elif self.overall_risk_score < 0.6:
            return "medium"
        elif self.overall_risk_score < 0.8:
            return "high"
        else:
            return "very_high"


@dataclass
class Position:
    """Trading position data."""
    id: str
    symbol: str
    exchange: str
    side: str  # "long" or "short"
    quantity: float
    entry_price: float
    current_price: float
    timestamp: float
    strategy: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def market_value(self) -> float:
        """Get current market value."""
        return self.quantity * self.current_price
    
    @property
    def unrealized_pnl(self) -> float:
        """Get unrealized P&L."""
        if self.side == "long":
            return self.quantity * (self.current_price - self.entry_price)
        else:
            return self.quantity * (self.entry_price - self.current_price)
    
    @property
    def unrealized_pnl_percentage(self) -> float:
        """Get unrealized P&L as percentage."""
        entry_value = self.quantity * self.entry_price
        return (self.unrealized_pnl / entry_value) * 100


@dataclass
class Alert:
    """Alert data structure."""
    id: str
    type: str
    severity: str  # "info", "warning", "error", "critical"
    title: str
    message: str
    timestamp: float
    data: Dict[str, Any] = field(default_factory=dict)
    acknowledged: bool = False
    
    def acknowledge(self):
        """Acknowledge the alert."""
        self.acknowledged = True


@dataclass
class PerformanceMetrics:
    """Performance metrics for the system."""
    timestamp: float
    latency_ms: Dict[str, float]  # Latency by component
    throughput: Dict[str, float]  # Messages per second by component
    error_rate: Dict[str, float]  # Error rate by component
    memory_usage_mb: float
    cpu_usage_percent: float
    active_connections: int
    processed_messages: int
    
    def get_overall_health_score(self) -> float:
        """Calculate overall system health score (0-1)."""
        # Simple health score based on key metrics
        latency_score = 1.0 - min(max(self.latency_ms.get("overall", 0) / 100, 0), 1)
        error_score = 1.0 - min(max(self.error_rate.get("overall", 0), 0), 1)
        resource_score = 1.0 - min(max((self.cpu_usage_percent + self.memory_usage_mb / 1024) / 200, 0), 1)
        
        return (latency_score + error_score + resource_score) / 3
