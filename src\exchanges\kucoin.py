"""
KuCoin exchange connector for ArbitrageVision.

This module implements the KuCoin exchange interface with WebSocket streaming
and REST API integration.
"""

import asyncio
import json
import time
import base64
import hmac
import hashlib
import uuid
from typing import Dict, List, Optional, Any
import websockets
import aiohttp
import structlog

from src.exchanges.base import (
    BaseExchange, OrderBook, OrderBookLevel, Trade, ConnectionStatus
)
from src.config.settings import ExchangeConfig


class KuCoinExchange(BaseExchange):
    """KuCoin exchange connector."""
    
    def __init__(self, config: ExchangeConfig):
        super().__init__(config)
        self.logger = structlog.get_logger("KuCoinExchange")
        
        # KuCoin specific settings
        self.base_url = config.base_url or "https://api.kucoin.com"
        self.ws_url = config.ws_url or "wss://ws-api.kucoin.com/endpoint"
        
        if config.sandbox:
            self.base_url = "https://openapi-sandbox.kucoin.com"
            self.ws_url = "wss://ws-api-sandbox.kucoin.com/endpoint"
        
        # Authentication
        self.api_key = config.api_key
        self.api_secret = config.api_secret
        self.passphrase = config.passphrase
        
        # WebSocket connection details
        self.ws_token = None
        self.ws_endpoint = None
        self.ping_interval = 18000  # 18 seconds
        self.ping_timeout = 10000   # 10 seconds
        
        # WebSocket subscriptions
        self.subscribed_symbols: set = set()
        
        # Session for HTTP requests
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def connect(self) -> bool:
        """Connect to KuCoin WebSocket and REST API."""
        try:
            self._update_status(ConnectionStatus.CONNECTING)
            
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30)
            )
            
            # Test REST API connection
            await self._test_connectivity()
            
            # Get WebSocket token and endpoint
            await self._get_websocket_token()
            
            # Connect WebSocket
            await self._connect_websocket()
            
            self._update_status(ConnectionStatus.CONNECTED)
            self.logger.info("Connected to KuCoin", exchange=self.name)
            return True
            
        except Exception as e:
            self.logger.error("Failed to connect to KuCoin", error=str(e))
            self._update_status(ConnectionStatus.ERROR)
            return False
    
    async def disconnect(self) -> None:
        """Disconnect from KuCoin."""
        try:
            # Close WebSocket connection
            if self.ws_connection:
                await self.ws_connection.close()
                self.ws_connection = None
            
            # Close HTTP session
            if self.session:
                await self.session.close()
                self.session = None
            
            self._update_status(ConnectionStatus.DISCONNECTED)
            self.logger.info("Disconnected from KuCoin", exchange=self.name)
            
        except Exception as e:
            self.logger.error("Error disconnecting from KuCoin", error=str(e))
    
    async def subscribe_order_book(self, symbol: str) -> bool:
        """Subscribe to order book updates for a symbol."""
        try:
            kucoin_symbol = self.denormalize_symbol(symbol)
            
            if symbol not in self.subscribed_symbols:
                self.subscribed_symbols.add(symbol)
                
                # If WebSocket is connected, subscribe to the channel
                if self.ws_connection:
                    await self._subscribe_level2(kucoin_symbol)
                
                self.logger.info("Subscribed to order book", symbol=symbol)
                return True
            
            return True
            
        except Exception as e:
            self.logger.error("Failed to subscribe to order book", symbol=symbol, error=str(e))
            return False
    
    async def unsubscribe_order_book(self, symbol: str) -> bool:
        """Unsubscribe from order book updates for a symbol."""
        try:
            kucoin_symbol = self.denormalize_symbol(symbol)
            
            if symbol in self.subscribed_symbols:
                self.subscribed_symbols.discard(symbol)
                
                # If WebSocket is connected, unsubscribe from the channel
                if self.ws_connection:
                    await self._unsubscribe_level2(kucoin_symbol)
                
                self.logger.info("Unsubscribed from order book", symbol=symbol)
                return True
            
            return True
            
        except Exception as e:
            self.logger.error("Failed to unsubscribe from order book", symbol=symbol, error=str(e))
            return False
    
    async def subscribe_trades(self, symbol: str) -> bool:
        """Subscribe to trade updates for a symbol."""
        try:
            kucoin_symbol = self.denormalize_symbol(symbol)
            
            # If WebSocket is connected, subscribe to trades
            if self.ws_connection:
                await self._subscribe_match(kucoin_symbol)
            
            self.logger.info("Subscribed to trades", symbol=symbol)
            return True
            
        except Exception as e:
            self.logger.error("Failed to subscribe to trades", symbol=symbol, error=str(e))
            return False
    
    async def unsubscribe_trades(self, symbol: str) -> bool:
        """Unsubscribe from trade updates for a symbol."""
        try:
            kucoin_symbol = self.denormalize_symbol(symbol)
            
            # If WebSocket is connected, unsubscribe from trades
            if self.ws_connection:
                await self._unsubscribe_match(kucoin_symbol)
            
            self.logger.info("Unsubscribed from trades", symbol=symbol)
            return True
            
        except Exception as e:
            self.logger.error("Failed to unsubscribe from trades", symbol=symbol, error=str(e))
            return False
    
    async def get_order_book_snapshot(self, symbol: str, depth: int = 20) -> Optional[OrderBook]:
        """Get order book snapshot from REST API."""
        try:
            if not self.session:
                return None
            
            kucoin_symbol = self.denormalize_symbol(symbol)
            url = f"{self.base_url}/api/v1/market/orderbook/level2_{depth}"
            params = {"symbol": kucoin_symbol}
            
            headers = self._get_auth_headers("GET", f"/api/v1/market/orderbook/level2_{depth}", "")
            
            async with self.session.get(url, params=params, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("code") == "200000":
                        return self._parse_order_book_snapshot(symbol, data["data"])
                    else:
                        self.logger.error("KuCoin API error", error=data.get("msg"))
                        return None
                else:
                    self.logger.error("Failed to get order book snapshot", 
                                    symbol=symbol, status=response.status)
                    return None
                    
        except Exception as e:
            self.logger.error("Error getting order book snapshot", symbol=symbol, error=str(e))
            return None
    
    async def get_trading_fees(self, symbol: str) -> Dict[str, float]:
        """Get trading fees for a symbol."""
        try:
            if not self.session:
                return {"maker": 0.001, "taker": 0.001}
            
            url = f"{self.base_url}/api/v1/base-fee"
            headers = self._get_auth_headers("GET", "/api/v1/base-fee", "")
            
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("code") == "200000":
                        fee_data = data["data"]
                        return {
                            "maker": float(fee_data.get("makerFeeRate", 0.001)),
                            "taker": float(fee_data.get("takerFeeRate", 0.001))
                        }
                
                # Return default fees
                return {"maker": 0.001, "taker": 0.001}
                    
        except Exception as e:
            self.logger.error("Error getting trading fees", symbol=symbol, error=str(e))
            return {"maker": 0.001, "taker": 0.001}
    
    def normalize_symbol(self, symbol: str) -> str:
        """Convert KuCoin symbol to normalized format (e.g., BTC-USDT -> BTC/USDT)."""
        return symbol.replace("-", "/")
    
    def denormalize_symbol(self, symbol: str) -> str:
        """Convert normalized symbol to KuCoin format (e.g., BTC/USDT -> BTC-USDT)."""
        return symbol.replace("/", "-")
    
    async def _test_connectivity(self) -> None:
        """Test REST API connectivity."""
        url = f"{self.base_url}/api/v1/timestamp"
        async with self.session.get(url) as response:
            if response.status != 200:
                raise Exception(f"KuCoin API connectivity test failed: {response.status}")
    
    async def _get_websocket_token(self) -> None:
        """Get WebSocket token and endpoint."""
        url = f"{self.base_url}/api/v1/bullet-public"
        headers = self._get_auth_headers("POST", "/api/v1/bullet-public", "")
        
        async with self.session.post(url, headers=headers) as response:
            if response.status == 200:
                data = await response.json()
                if data.get("code") == "200000":
                    token_data = data["data"]
                    self.ws_token = token_data["token"]
                    
                    # Get the first available endpoint
                    instances = token_data["instanceServers"]
                    if instances:
                        instance = instances[0]
                        self.ws_endpoint = instance["endpoint"]
                        self.ping_interval = instance.get("pingInterval", 18000)
                        self.ping_timeout = instance.get("pingTimeout", 10000)
                else:
                    raise Exception(f"Failed to get WebSocket token: {data.get('msg')}")
            else:
                raise Exception(f"Failed to get WebSocket token: HTTP {response.status}")
    
    async def _connect_websocket(self) -> None:
        """Connect to KuCoin WebSocket."""
        try:
            if not self.ws_token or not self.ws_endpoint:
                raise Exception("WebSocket token or endpoint not available")
            
            ws_url = f"{self.ws_endpoint}?token={self.ws_token}&[connectId={uuid.uuid4()}]"
            self.ws_connection = await websockets.connect(ws_url)
            
            # Start WebSocket message handler
            self.ws_task = asyncio.create_task(self._handle_websocket_messages())
            
            # Start ping task
            self.ping_task = asyncio.create_task(self._ping_loop())
            
            # Resubscribe to existing symbols
            await self._resubscribe_all()
            
        except Exception as e:
            self.logger.error("Failed to connect WebSocket", error=str(e))
            raise
    
    async def _handle_websocket_messages(self) -> None:
        """Handle incoming WebSocket messages."""
        try:
            async for message in self.ws_connection:
                try:
                    data = json.loads(message)
                    await self._process_websocket_message(data)
                except json.JSONDecodeError as e:
                    self.logger.error("Failed to parse WebSocket message", error=str(e))
                except Exception as e:
                    self.logger.error("Error processing WebSocket message", error=str(e))
                    
        except websockets.exceptions.ConnectionClosed:
            self.logger.warning("WebSocket connection closed")
            self._update_status(ConnectionStatus.DISCONNECTED)
            # Attempt to reconnect
            await self._reconnect()
        except Exception as e:
            self.logger.error("WebSocket error", error=str(e))
            self._update_status(ConnectionStatus.ERROR)
    
    async def _process_websocket_message(self, data: Dict[str, Any]) -> None:
        """Process WebSocket message."""
        msg_type = data.get("type")
        
        if msg_type == "message":
            topic = data.get("topic", "")
            subject = data.get("subject", "")
            
            if "/market/level2" in topic:
                await self._handle_level2_message(data)
            elif "/market/match" in topic:
                await self._handle_match_message(data)
        
        elif msg_type == "welcome":
            self.logger.info("WebSocket welcome message received")
        
        elif msg_type == "ack":
            self.logger.debug("WebSocket ack received", id=data.get("id"))
        
        elif msg_type == "error":
            self.logger.error("WebSocket error message", error=data.get("data"))
        
        elif msg_type == "pong":
            # Update last ping time
            self.last_ping = time.time()
    
    async def _handle_level2_message(self, data: Dict[str, Any]) -> None:
        """Handle level 2 order book message."""
        try:
            topic = data.get("topic", "")
            symbol_part = topic.split(":")[-1] if ":" in topic else ""
            symbol = self.normalize_symbol(symbol_part)
            
            subject = data.get("subject", "")
            message_data = data.get("data", {})
            
            if subject == "level2":
                order_book = self._parse_order_book_update(symbol, message_data)
                if order_book:
                    self._notify_order_book_update(order_book)
                    
        except Exception as e:
            self.logger.error("Error handling level2 message", error=str(e))
    
    async def _handle_match_message(self, data: Dict[str, Any]) -> None:
        """Handle trade match message."""
        try:
            topic = data.get("topic", "")
            symbol_part = topic.split(":")[-1] if ":" in topic else ""
            symbol = self.normalize_symbol(symbol_part)
            
            message_data = data.get("data", {})
            trade = self._parse_trade(symbol, message_data)
            if trade:
                self._notify_trade_update(trade)
                
        except Exception as e:
            self.logger.error("Error handling match message", error=str(e))

    def _parse_order_book_snapshot(self, symbol: str, data: Dict[str, Any]) -> OrderBook:
        """Parse order book snapshot."""
        timestamp = time.time()

        bids = [
            OrderBookLevel(price=float(bid[0]), quantity=float(bid[1]), timestamp=timestamp)
            for bid in data.get("bids", [])
        ]

        asks = [
            OrderBookLevel(price=float(ask[0]), quantity=float(ask[1]), timestamp=timestamp)
            for ask in data.get("asks", [])
        ]

        return OrderBook(
            symbol=symbol,
            exchange=self.name,
            bids=bids,
            asks=asks,
            timestamp=timestamp,
            sequence=data.get("sequence")
        )

    def _parse_order_book_update(self, symbol: str, data: Dict[str, Any]) -> Optional[OrderBook]:
        """Parse order book update."""
        try:
            timestamp = int(data.get("time", time.time() * 1000)) / 1000

            # Get current order book
            current_ob = self.get_order_book(symbol)
            if not current_ob:
                return None

            bids = current_ob.bids.copy()
            asks = current_ob.asks.copy()

            # Apply changes
            changes = data.get("changes", {})

            # Update bids
            for bid_change in changes.get("bids", []):
                price = float(bid_change[0])
                quantity = float(bid_change[1])

                if quantity == 0:
                    # Remove price level
                    bids = [b for b in bids if b.price != price]
                else:
                    # Update or add price level
                    updated = False
                    for i, bid in enumerate(bids):
                        if bid.price == price:
                            bids[i] = OrderBookLevel(price=price, quantity=quantity, timestamp=timestamp)
                            updated = True
                            break

                    if not updated:
                        bids.append(OrderBookLevel(price=price, quantity=quantity, timestamp=timestamp))

            # Update asks
            for ask_change in changes.get("asks", []):
                price = float(ask_change[0])
                quantity = float(ask_change[1])

                if quantity == 0:
                    # Remove price level
                    asks = [a for a in asks if a.price != price]
                else:
                    # Update or add price level
                    updated = False
                    for i, ask in enumerate(asks):
                        if ask.price == price:
                            asks[i] = OrderBookLevel(price=price, quantity=quantity, timestamp=timestamp)
                            updated = True
                            break

                    if not updated:
                        asks.append(OrderBookLevel(price=price, quantity=quantity, timestamp=timestamp))

            # Sort order book
            bids.sort(key=lambda x: x.price, reverse=True)
            asks.sort(key=lambda x: x.price)

            return OrderBook(
                symbol=symbol,
                exchange=self.name,
                bids=bids,
                asks=asks,
                timestamp=timestamp,
                sequence=data.get("sequenceEnd")
            )

        except Exception as e:
            self.logger.error("Error parsing order book update", symbol=symbol, error=str(e))
            return None

    def _parse_trade(self, symbol: str, data: Dict[str, Any]) -> Optional[Trade]:
        """Parse trade from match message."""
        try:
            return Trade(
                symbol=symbol,
                exchange=self.name,
                price=float(data["price"]),
                quantity=float(data["size"]),
                side=data["side"],
                timestamp=int(data["time"]) / 1000000000,  # Convert nanoseconds to seconds
                trade_id=data.get("tradeId")
            )

        except Exception as e:
            self.logger.error("Error parsing trade", symbol=symbol, error=str(e))
            return None

    async def _subscribe_level2(self, symbol: str) -> None:
        """Subscribe to level 2 order book updates."""
        if self.ws_connection:
            message = {
                "id": str(int(time.time() * 1000)),
                "type": "subscribe",
                "topic": f"/market/level2:{symbol}",
                "privateChannel": False,
                "response": True
            }
            await self.ws_connection.send(json.dumps(message))

    async def _unsubscribe_level2(self, symbol: str) -> None:
        """Unsubscribe from level 2 order book updates."""
        if self.ws_connection:
            message = {
                "id": str(int(time.time() * 1000)),
                "type": "unsubscribe",
                "topic": f"/market/level2:{symbol}",
                "privateChannel": False,
                "response": True
            }
            await self.ws_connection.send(json.dumps(message))

    async def _subscribe_match(self, symbol: str) -> None:
        """Subscribe to trade match updates."""
        if self.ws_connection:
            message = {
                "id": str(int(time.time() * 1000)),
                "type": "subscribe",
                "topic": f"/market/match:{symbol}",
                "privateChannel": False,
                "response": True
            }
            await self.ws_connection.send(json.dumps(message))

    async def _unsubscribe_match(self, symbol: str) -> None:
        """Unsubscribe from trade match updates."""
        if self.ws_connection:
            message = {
                "id": str(int(time.time() * 1000)),
                "type": "unsubscribe",
                "topic": f"/market/match:{symbol}",
                "privateChannel": False,
                "response": True
            }
            await self.ws_connection.send(json.dumps(message))

    async def _resubscribe_all(self) -> None:
        """Resubscribe to all symbols after reconnection."""
        for symbol in self.subscribed_symbols:
            kucoin_symbol = self.denormalize_symbol(symbol)
            await self._subscribe_level2(kucoin_symbol)

    async def _ping_loop(self) -> None:
        """Send periodic ping messages."""
        try:
            while True:
                await asyncio.sleep(self.ping_interval / 1000)  # Convert to seconds
                if self.ws_connection:
                    ping_message = {
                        "id": str(int(time.time() * 1000)),
                        "type": "ping"
                    }
                    await self.ws_connection.send(json.dumps(ping_message))
        except asyncio.CancelledError:
            pass
        except Exception as e:
            self.logger.error("Error in ping loop", error=str(e))

    def _get_auth_headers(self, method: str, path: str, body: str) -> Dict[str, str]:
        """Generate authentication headers for REST API."""
        if not (self.api_key and self.api_secret and self.passphrase):
            return {"Content-Type": "application/json"}

        timestamp = str(int(time.time() * 1000))
        str_to_sign = timestamp + method + path + body
        signature = base64.b64encode(
            hmac.new(
                self.api_secret.encode(),
                str_to_sign.encode(),
                hashlib.sha256
            ).digest()
        ).decode()

        passphrase = base64.b64encode(
            hmac.new(
                self.api_secret.encode(),
                self.passphrase.encode(),
                hashlib.sha256
            ).digest()
        ).decode()

        return {
            "KC-API-SIGN": signature,
            "KC-API-TIMESTAMP": timestamp,
            "KC-API-KEY": self.api_key,
            "KC-API-PASSPHRASE": passphrase,
            "KC-API-KEY-VERSION": "2",
            "Content-Type": "application/json"
        }

    async def _send_ping(self) -> None:
        """Send ping to WebSocket connection."""
        if self.ws_connection:
            ping_message = {
                "id": str(int(time.time() * 1000)),
                "type": "ping"
            }
            await self.ws_connection.send(json.dumps(ping_message))
