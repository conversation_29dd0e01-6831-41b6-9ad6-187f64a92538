"""
Kraken exchange connector for ArbitrageVision.

This module implements the Kraken exchange interface with WebSocket streaming
and REST API integration.
"""

import asyncio
import json
import time
import base64
import hmac
import hashlib
import urllib.parse
from typing import Dict, List, Optional, Any
import websockets
import aiohttp
import structlog

from src.exchanges.base import (
    BaseExchange, OrderBook, OrderBookLevel, Trade, ConnectionStatus
)
from src.config.settings import ExchangeConfig


class KrakenExchange(BaseExchange):
    """Kraken exchange connector."""
    
    def __init__(self, config: ExchangeConfig):
        super().__init__(config)
        self.logger = structlog.get_logger("KrakenExchange")
        
        # Kraken specific settings
        self.base_url = config.base_url or "https://api.kraken.com"
        self.ws_url = config.ws_url or "wss://ws.kraken.com"
        
        # Authentication
        self.api_key = config.api_key
        self.api_secret = config.api_secret
        
        # WebSocket subscriptions
        self.subscribed_symbols: set = set()
        self.subscription_ids: Dict[str, int] = {}
        
        # Session for HTTP requests
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Kraken symbol mapping
        self.symbol_map = {
            "BTC/USD": "XBTUSD",
            "ETH/USD": "ETHUSD",
            "LTC/USD": "LTCUSD",
            "BCH/USD": "BCHUSD",
            "ADA/USD": "ADAUSD",
            "DOT/USD": "DOTUSD",
            "LINK/USD": "LINKUSD",
            "XRP/USD": "XRPUSD"
        }
        
        # Reverse mapping
        self.reverse_symbol_map = {v: k for k, v in self.symbol_map.items()}
        
    async def connect(self) -> bool:
        """Connect to Kraken WebSocket and REST API."""
        try:
            self._update_status(ConnectionStatus.CONNECTING)
            
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30)
            )
            
            # Test REST API connection
            await self._test_connectivity()
            
            # Connect WebSocket
            await self._connect_websocket()
            
            self._update_status(ConnectionStatus.CONNECTED)
            self.logger.info("Connected to Kraken", exchange=self.name)
            return True
            
        except Exception as e:
            self.logger.error("Failed to connect to Kraken", error=str(e))
            self._update_status(ConnectionStatus.ERROR)
            return False
    
    async def disconnect(self) -> None:
        """Disconnect from Kraken."""
        try:
            # Close WebSocket connection
            if self.ws_connection:
                await self.ws_connection.close()
                self.ws_connection = None
            
            # Close HTTP session
            if self.session:
                await self.session.close()
                self.session = None
            
            self._update_status(ConnectionStatus.DISCONNECTED)
            self.logger.info("Disconnected from Kraken", exchange=self.name)
            
        except Exception as e:
            self.logger.error("Error disconnecting from Kraken", error=str(e))
    
    async def subscribe_order_book(self, symbol: str) -> bool:
        """Subscribe to order book updates for a symbol."""
        try:
            kraken_symbol = self.denormalize_symbol(symbol)
            
            if symbol not in self.subscribed_symbols:
                self.subscribed_symbols.add(symbol)
                
                # If WebSocket is connected, subscribe to the channel
                if self.ws_connection:
                    await self._subscribe_book(kraken_symbol)
                
                self.logger.info("Subscribed to order book", symbol=symbol)
                return True
            
            return True
            
        except Exception as e:
            self.logger.error("Failed to subscribe to order book", symbol=symbol, error=str(e))
            return False
    
    async def unsubscribe_order_book(self, symbol: str) -> bool:
        """Unsubscribe from order book updates for a symbol."""
        try:
            kraken_symbol = self.denormalize_symbol(symbol)
            
            if symbol in self.subscribed_symbols:
                self.subscribed_symbols.discard(symbol)
                
                # If WebSocket is connected, unsubscribe from the channel
                if self.ws_connection:
                    await self._unsubscribe_book(kraken_symbol)
                
                self.logger.info("Unsubscribed from order book", symbol=symbol)
                return True
            
            return True
            
        except Exception as e:
            self.logger.error("Failed to unsubscribe from order book", symbol=symbol, error=str(e))
            return False
    
    async def subscribe_trades(self, symbol: str) -> bool:
        """Subscribe to trade updates for a symbol."""
        try:
            kraken_symbol = self.denormalize_symbol(symbol)
            
            # If WebSocket is connected, subscribe to trades
            if self.ws_connection:
                await self._subscribe_trades(kraken_symbol)
            
            self.logger.info("Subscribed to trades", symbol=symbol)
            return True
            
        except Exception as e:
            self.logger.error("Failed to subscribe to trades", symbol=symbol, error=str(e))
            return False
    
    async def unsubscribe_trades(self, symbol: str) -> bool:
        """Unsubscribe from trade updates for a symbol."""
        try:
            kraken_symbol = self.denormalize_symbol(symbol)
            
            # If WebSocket is connected, unsubscribe from trades
            if self.ws_connection:
                await self._unsubscribe_trades(kraken_symbol)
            
            self.logger.info("Unsubscribed from trades", symbol=symbol)
            return True
            
        except Exception as e:
            self.logger.error("Failed to unsubscribe from trades", symbol=symbol, error=str(e))
            return False
    
    async def get_order_book_snapshot(self, symbol: str, depth: int = 20) -> Optional[OrderBook]:
        """Get order book snapshot from REST API."""
        try:
            if not self.session:
                return None
            
            kraken_symbol = self.denormalize_symbol(symbol)
            url = f"{self.base_url}/0/public/Depth"
            params = {"pair": kraken_symbol, "count": depth}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("error"):
                        self.logger.error("Kraken API error", error=data["error"])
                        return None
                    
                    # Kraken returns data with the pair name as key
                    result = data.get("result", {})
                    pair_data = result.get(kraken_symbol) or result.get(list(result.keys())[0])
                    
                    if pair_data:
                        return self._parse_order_book_snapshot(symbol, pair_data)
                    
                else:
                    self.logger.error("Failed to get order book snapshot", 
                                    symbol=symbol, status=response.status)
                    return None
                    
        except Exception as e:
            self.logger.error("Error getting order book snapshot", symbol=symbol, error=str(e))
            return None
    
    async def get_trading_fees(self, symbol: str) -> Dict[str, float]:
        """Get trading fees for a symbol."""
        # Kraken has tiered fees, return standard rates
        return {
            "maker": self.config.fees.get("maker", 0.0016),
            "taker": self.config.fees.get("taker", 0.0026)
        }
    
    def normalize_symbol(self, symbol: str) -> str:
        """Convert Kraken symbol to normalized format."""
        return self.reverse_symbol_map.get(symbol, symbol)
    
    def denormalize_symbol(self, symbol: str) -> str:
        """Convert normalized symbol to Kraken format."""
        return self.symbol_map.get(symbol, symbol)
    
    async def _test_connectivity(self) -> None:
        """Test REST API connectivity."""
        url = f"{self.base_url}/0/public/Time"
        async with self.session.get(url) as response:
            if response.status != 200:
                raise Exception(f"Kraken API connectivity test failed: {response.status}")
    
    async def _connect_websocket(self) -> None:
        """Connect to Kraken WebSocket."""
        try:
            self.ws_connection = await websockets.connect(self.ws_url)
            
            # Start WebSocket message handler
            self.ws_task = asyncio.create_task(self._handle_websocket_messages())
            
            # Resubscribe to existing symbols
            await self._resubscribe_all()
            
        except Exception as e:
            self.logger.error("Failed to connect WebSocket", error=str(e))
            raise
    
    async def _handle_websocket_messages(self) -> None:
        """Handle incoming WebSocket messages."""
        try:
            async for message in self.ws_connection:
                try:
                    data = json.loads(message)
                    await self._process_websocket_message(data)
                except json.JSONDecodeError as e:
                    self.logger.error("Failed to parse WebSocket message", error=str(e))
                except Exception as e:
                    self.logger.error("Error processing WebSocket message", error=str(e))
                    
        except websockets.exceptions.ConnectionClosed:
            self.logger.warning("WebSocket connection closed")
            self._update_status(ConnectionStatus.DISCONNECTED)
            # Attempt to reconnect
            await self._reconnect()
        except Exception as e:
            self.logger.error("WebSocket error", error=str(e))
            self._update_status(ConnectionStatus.ERROR)
    
    async def _process_websocket_message(self, data: Any) -> None:
        """Process WebSocket message."""
        if isinstance(data, dict):
            # Handle subscription status messages
            if "event" in data:
                await self._handle_event_message(data)
        elif isinstance(data, list) and len(data) >= 4:
            # Handle data messages
            channel_id = data[0]
            channel_data = data[1]
            channel_name = data[2]
            pair = data[3]
            
            if "book" in channel_name:
                await self._handle_book_update(pair, channel_data)
            elif "trade" in channel_name:
                await self._handle_trade_update(pair, channel_data)
    
    async def _handle_event_message(self, data: Dict[str, Any]) -> None:
        """Handle WebSocket event messages."""
        event = data.get("event")
        
        if event == "subscriptionStatus":
            status = data.get("status")
            if status == "subscribed":
                self.logger.info("Subscription confirmed", 
                               pair=data.get("pair"), 
                               subscription=data.get("subscription"))
            elif status == "error":
                self.logger.error("Subscription error", error=data.get("errorMessage"))
        elif event == "systemStatus":
            self.logger.info("System status", status=data.get("status"))
        elif event == "heartbeat":
            # Update last ping time
            self.last_ping = time.time()
    
    async def _handle_book_update(self, pair: str, data: Dict[str, Any]) -> None:
        """Handle order book update."""
        try:
            symbol = self.normalize_symbol(pair)
            
            if "bs" in data or "as" in data:
                # Snapshot
                order_book = self._parse_order_book_snapshot(symbol, data)
            else:
                # Update
                order_book = self._parse_order_book_update(symbol, data)
            
            if order_book:
                self._notify_order_book_update(order_book)
                
        except Exception as e:
            self.logger.error("Error handling book update", pair=pair, error=str(e))
    
    async def _handle_trade_update(self, pair: str, data: List[Any]) -> None:
        """Handle trade update."""
        try:
            symbol = self.normalize_symbol(pair)
            
            for trade_data in data:
                trade = self._parse_trade(symbol, trade_data)
                if trade:
                    self._notify_trade_update(trade)
                    
        except Exception as e:
            self.logger.error("Error handling trade update", pair=pair, error=str(e))

    def _parse_order_book_snapshot(self, symbol: str, data: Dict[str, Any]) -> OrderBook:
        """Parse order book snapshot."""
        timestamp = time.time()

        bids = []
        asks = []

        # Handle both snapshot formats
        if "bids" in data and "asks" in data:
            # REST API format
            for bid in data["bids"]:
                bids.append(OrderBookLevel(
                    price=float(bid[0]),
                    quantity=float(bid[1]),
                    timestamp=timestamp
                ))

            for ask in data["asks"]:
                asks.append(OrderBookLevel(
                    price=float(ask[0]),
                    quantity=float(ask[1]),
                    timestamp=timestamp
                ))

        elif "bs" in data and "as" in data:
            # WebSocket snapshot format
            for bid in data["bs"]:
                bids.append(OrderBookLevel(
                    price=float(bid[0]),
                    quantity=float(bid[1]),
                    timestamp=float(bid[2]) if len(bid) > 2 else timestamp
                ))

            for ask in data["as"]:
                asks.append(OrderBookLevel(
                    price=float(ask[0]),
                    quantity=float(ask[1]),
                    timestamp=float(ask[2]) if len(ask) > 2 else timestamp
                ))

        return OrderBook(
            symbol=symbol,
            exchange=self.name,
            bids=bids,
            asks=asks,
            timestamp=timestamp
        )

    def _parse_order_book_update(self, symbol: str, data: Dict[str, Any]) -> Optional[OrderBook]:
        """Parse order book update."""
        try:
            timestamp = time.time()

            # Get current order book
            current_ob = self.get_order_book(symbol)
            if not current_ob:
                return None

            bids = current_ob.bids.copy()
            asks = current_ob.asks.copy()

            # Apply bid updates
            if "b" in data:
                for bid_update in data["b"]:
                    price = float(bid_update[0])
                    quantity = float(bid_update[1])

                    if quantity == 0:
                        # Remove price level
                        bids = [b for b in bids if b.price != price]
                    else:
                        # Update or add price level
                        updated = False
                        for i, bid in enumerate(bids):
                            if bid.price == price:
                                bids[i] = OrderBookLevel(price=price, quantity=quantity, timestamp=timestamp)
                                updated = True
                                break

                        if not updated:
                            bids.append(OrderBookLevel(price=price, quantity=quantity, timestamp=timestamp))

            # Apply ask updates
            if "a" in data:
                for ask_update in data["a"]:
                    price = float(ask_update[0])
                    quantity = float(ask_update[1])

                    if quantity == 0:
                        # Remove price level
                        asks = [a for a in asks if a.price != price]
                    else:
                        # Update or add price level
                        updated = False
                        for i, ask in enumerate(asks):
                            if ask.price == price:
                                asks[i] = OrderBookLevel(price=price, quantity=quantity, timestamp=timestamp)
                                updated = True
                                break

                        if not updated:
                            asks.append(OrderBookLevel(price=price, quantity=quantity, timestamp=timestamp))

            # Sort order book
            bids.sort(key=lambda x: x.price, reverse=True)
            asks.sort(key=lambda x: x.price)

            return OrderBook(
                symbol=symbol,
                exchange=self.name,
                bids=bids,
                asks=asks,
                timestamp=timestamp
            )

        except Exception as e:
            self.logger.error("Error parsing order book update", symbol=symbol, error=str(e))
            return None

    def _parse_trade(self, symbol: str, trade_data: List[Any]) -> Optional[Trade]:
        """Parse trade from WebSocket message."""
        try:
            # Kraken trade format: [price, volume, time, side, orderType, misc]
            return Trade(
                symbol=symbol,
                exchange=self.name,
                price=float(trade_data[0]),
                quantity=float(trade_data[1]),
                side="buy" if trade_data[3] == "b" else "sell",
                timestamp=float(trade_data[2]),
                trade_id=None
            )

        except Exception as e:
            self.logger.error("Error parsing trade", symbol=symbol, error=str(e))
            return None

    async def _subscribe_book(self, pair: str) -> None:
        """Subscribe to order book updates."""
        if self.ws_connection:
            message = {
                "event": "subscribe",
                "pair": [pair],
                "subscription": {"name": "book", "depth": 25}
            }
            await self.ws_connection.send(json.dumps(message))

    async def _unsubscribe_book(self, pair: str) -> None:
        """Unsubscribe from order book updates."""
        if self.ws_connection:
            message = {
                "event": "unsubscribe",
                "pair": [pair],
                "subscription": {"name": "book"}
            }
            await self.ws_connection.send(json.dumps(message))

    async def _subscribe_trades(self, pair: str) -> None:
        """Subscribe to trade updates."""
        if self.ws_connection:
            message = {
                "event": "subscribe",
                "pair": [pair],
                "subscription": {"name": "trade"}
            }
            await self.ws_connection.send(json.dumps(message))

    async def _unsubscribe_trades(self, pair: str) -> None:
        """Unsubscribe from trade updates."""
        if self.ws_connection:
            message = {
                "event": "unsubscribe",
                "pair": [pair],
                "subscription": {"name": "trade"}
            }
            await self.ws_connection.send(json.dumps(message))

    async def _resubscribe_all(self) -> None:
        """Resubscribe to all symbols after reconnection."""
        for symbol in self.subscribed_symbols:
            kraken_symbol = self.denormalize_symbol(symbol)
            await self._subscribe_book(kraken_symbol)

    async def _send_ping(self) -> None:
        """Send ping to WebSocket connection."""
        if self.ws_connection:
            message = {"event": "ping"}
            await self.ws_connection.send(json.dumps(message))
