"""
ArbitrageVision - Main Application Entry Point

High-frequency arbitrage detection and execution system for cryptocurrency exchanges.
"""

import asyncio
import logging
import signal
import sys
from contextlib import asynccontextmanager
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from prometheus_client import start_http_server, Counter, Histogram, Gauge
import structlog

from src.config.settings import Settings, get_settings
from src.data.stream_manager import StreamManager
from src.arbitrage.simple import SimpleArbitrageDetector
from src.arbitrage.triangular import TriangularArbitrageDetector
from src.arbitrage.statistical import StatisticalArbitrageDetector
from src.analysis.risk_manager import RiskManager
from src.alerts.notification import NotificationManager
from src.api.routes import router as api_router
from src.api.websocket import websocket_router


# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

# Prometheus metrics
ARBITRAGE_OPPORTUNITIES = Counter(
    'arbitrage_opportunities_total',
    'Total arbitrage opportunities detected',
    ['exchange_pair', 'strategy', 'profit_tier']
)

ARBITRAGE_PROFIT = Histogram(
    'arbitrage_profit_usd',
    'Profit amount in USD',
    ['exchange_pair', 'strategy'],
    buckets=[1, 5, 10, 25, 50, 100, 250, 500, 1000]
)

ORDER_BOOK_LATENCY = Histogram(
    'order_book_update_latency_ms',
    'Order book update latency in milliseconds',
    ['exchange'],
    buckets=[0.1, 0.5, 1, 2, 5, 10, 25, 50, 100]
)

EXCHANGE_CONNECTION_STATUS = Gauge(
    'exchange_connection_status',
    'Exchange connection status',
    ['exchange', 'connection_type']
)

RISK_SCORE = Gauge('risk_score', 'Current risk score', ['risk_type'])
ACTIVE_POSITIONS = Gauge('active_positions_count', 'Active positions', ['exchange', 'strategy'])


class ArbitrageVisionApp:
    """Main application class for ArbitrageVision."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.stream_manager: StreamManager = None
        self.simple_detector: SimpleArbitrageDetector = None
        self.triangular_detector: TriangularArbitrageDetector = None
        self.statistical_detector: StatisticalArbitrageDetector = None
        self.risk_manager: RiskManager = None
        self.notification_manager: NotificationManager = None
        self.running = False
        
    async def initialize(self):
        """Initialize all application components."""
        logger.info("Initializing ArbitrageVision application")
        
        try:
            # Initialize core components
            self.stream_manager = StreamManager(self.settings)
            await self.stream_manager.initialize()
            
            # Initialize arbitrage detectors
            self.simple_detector = SimpleArbitrageDetector(self.settings)
            self.triangular_detector = TriangularArbitrageDetector(self.settings)
            self.statistical_detector = StatisticalArbitrageDetector(self.settings)
            
            # Initialize risk manager
            self.risk_manager = RiskManager(self.settings)
            await self.risk_manager.initialize()
            
            # Initialize notification manager
            self.notification_manager = NotificationManager(self.settings)
            await self.notification_manager.initialize()
            
            # Connect detectors to stream manager
            self.stream_manager.add_subscriber(self.simple_detector.process_update)
            self.stream_manager.add_subscriber(self.triangular_detector.process_update)
            self.stream_manager.add_subscriber(self.statistical_detector.process_update)
            
            # Connect detectors to risk manager
            self.simple_detector.add_opportunity_handler(self.risk_manager.evaluate_opportunity)
            self.triangular_detector.add_opportunity_handler(self.risk_manager.evaluate_opportunity)
            self.statistical_detector.add_opportunity_handler(self.risk_manager.evaluate_opportunity)
            
            # Connect risk manager to notification manager
            self.risk_manager.add_alert_handler(self.notification_manager.send_alert)
            
            logger.info("Application initialization completed successfully")
            
        except Exception as e:
            logger.error("Failed to initialize application", error=str(e))
            raise
    
    async def start(self):
        """Start the application."""
        logger.info("Starting ArbitrageVision application")
        
        try:
            self.running = True
            
            # Start all components
            await self.stream_manager.start()
            await self.simple_detector.start()
            await self.triangular_detector.start()
            await self.statistical_detector.start()
            await self.risk_manager.start()
            await self.notification_manager.start()
            
            logger.info("Application started successfully")
            
        except Exception as e:
            logger.error("Failed to start application", error=str(e))
            await self.stop()
            raise
    
    async def stop(self):
        """Stop the application gracefully."""
        logger.info("Stopping ArbitrageVision application")
        
        self.running = False
        
        # Stop all components in reverse order
        if self.notification_manager:
            await self.notification_manager.stop()
        
        if self.risk_manager:
            await self.risk_manager.stop()
        
        if self.statistical_detector:
            await self.statistical_detector.stop()
        
        if self.triangular_detector:
            await self.triangular_detector.stop()
        
        if self.simple_detector:
            await self.simple_detector.stop()
        
        if self.stream_manager:
            await self.stream_manager.stop()
        
        logger.info("Application stopped successfully")
    
    def get_status(self) -> Dict[str, Any]:
        """Get application status."""
        return {
            "running": self.running,
            "components": {
                "stream_manager": self.stream_manager.is_running() if self.stream_manager else False,
                "simple_detector": self.simple_detector.is_running() if self.simple_detector else False,
                "triangular_detector": self.triangular_detector.is_running() if self.triangular_detector else False,
                "statistical_detector": self.statistical_detector.is_running() if self.statistical_detector else False,
                "risk_manager": self.risk_manager.is_running() if self.risk_manager else False,
                "notification_manager": self.notification_manager.is_running() if self.notification_manager else False,
            },
            "exchanges": self.stream_manager.get_exchange_status() if self.stream_manager else {},
            "metrics": {
                "opportunities_detected": ARBITRAGE_OPPORTUNITIES._value._value,
                "active_positions": sum(ACTIVE_POSITIONS._value.values()),
                "current_risk_score": RISK_SCORE._value.get("overall", 0),
            }
        }


# Global application instance
app_instance: ArbitrageVisionApp = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global app_instance
    
    settings = get_settings()
    app_instance = ArbitrageVisionApp(settings)
    
    # Start Prometheus metrics server
    if settings.monitoring.prometheus.enabled:
        start_http_server(settings.monitoring.prometheus.port)
        logger.info(f"Prometheus metrics server started on port {settings.monitoring.prometheus.port}")
    
    # Initialize and start the application
    await app_instance.initialize()
    await app_instance.start()
    
    # Setup signal handlers
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, shutting down gracefully")
        asyncio.create_task(app_instance.stop())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    yield
    
    # Cleanup
    await app_instance.stop()


# Create FastAPI application
app = FastAPI(
    title="ArbitrageVision API",
    description="Real-Time Crypto Arbitrage Detection System",
    version="1.0.0",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)

# Include routers
app.include_router(api_router, prefix="/api/v1")
app.include_router(websocket_router, prefix="/ws")


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    if app_instance is None:
        raise HTTPException(status_code=503, detail="Application not initialized")
    
    status = app_instance.get_status()
    
    if not status["running"]:
        raise HTTPException(status_code=503, detail="Application not running")
    
    return {
        "status": "healthy",
        "timestamp": structlog.processors.TimeStamper(fmt="iso")(),
        "details": status
    }


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "name": "ArbitrageVision",
        "description": "Real-Time Crypto Arbitrage Detection System",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs",
        "health": "/health"
    }


if __name__ == "__main__":
    # Development server
    uvicorn.run(
        "src.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
