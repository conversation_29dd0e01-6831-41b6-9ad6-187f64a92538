"""
Binance exchange connector for ArbitrageVision.

This module implements the Binance exchange interface with WebSocket streaming
and REST API integration.
"""

import asyncio
import json
import time
import hmac
import hashlib
from typing import Dict, List, Optional, Any
from urllib.parse import urlencode
import websockets
import aiohttp
import structlog

from src.exchanges.base import (
    BaseExchange, OrderBook, OrderBookLevel, Trade, ConnectionStatus
)
from src.config.settings import ExchangeConfig


class BinanceExchange(BaseExchange):
    """Binance exchange connector."""
    
    def __init__(self, config: ExchangeConfig):
        super().__init__(config)
        self.logger = structlog.get_logger("BinanceExchange")
        
        # Binance specific settings
        self.base_url = config.base_url or "https://api.binance.com"
        self.ws_url = config.ws_url or "wss://stream.binance.com:9443/ws"
        
        if config.testnet:
            self.base_url = "https://testnet.binance.vision"
            self.ws_url = "wss://testnet.binance.vision/ws"
        
        # WebSocket stream management
        self.stream_names: List[str] = []
        self.subscribed_symbols: set = set()
        
        # Session for HTTP requests
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def connect(self) -> bool:
        """Connect to Binance WebSocket and REST API."""
        try:
            self._update_status(ConnectionStatus.CONNECTING)
            
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30)
            )
            
            # Test REST API connection
            await self._test_connectivity()
            
            # Connect WebSocket
            await self._connect_websocket()
            
            self._update_status(ConnectionStatus.CONNECTED)
            self.logger.info("Connected to Binance", exchange=self.name)
            return True
            
        except Exception as e:
            self.logger.error("Failed to connect to Binance", error=str(e))
            self._update_status(ConnectionStatus.ERROR)
            return False
    
    async def disconnect(self) -> None:
        """Disconnect from Binance."""
        try:
            # Close WebSocket connection
            if self.ws_connection:
                await self.ws_connection.close()
                self.ws_connection = None
            
            # Close HTTP session
            if self.session:
                await self.session.close()
                self.session = None
            
            self._update_status(ConnectionStatus.DISCONNECTED)
            self.logger.info("Disconnected from Binance", exchange=self.name)
            
        except Exception as e:
            self.logger.error("Error disconnecting from Binance", error=str(e))
    
    async def subscribe_order_book(self, symbol: str) -> bool:
        """Subscribe to order book updates for a symbol."""
        try:
            binance_symbol = self.denormalize_symbol(symbol)
            stream_name = f"{binance_symbol.lower()}@depth"
            
            if stream_name not in self.stream_names:
                self.stream_names.append(stream_name)
                self.subscribed_symbols.add(symbol)
                
                # If WebSocket is connected, subscribe to the stream
                if self.ws_connection:
                    await self._subscribe_stream(stream_name)
                
                self.logger.info("Subscribed to order book", symbol=symbol)
                return True
            
            return True
            
        except Exception as e:
            self.logger.error("Failed to subscribe to order book", symbol=symbol, error=str(e))
            return False
    
    async def unsubscribe_order_book(self, symbol: str) -> bool:
        """Unsubscribe from order book updates for a symbol."""
        try:
            binance_symbol = self.denormalize_symbol(symbol)
            stream_name = f"{binance_symbol.lower()}@depth"
            
            if stream_name in self.stream_names:
                self.stream_names.remove(stream_name)
                self.subscribed_symbols.discard(symbol)
                
                # If WebSocket is connected, unsubscribe from the stream
                if self.ws_connection:
                    await self._unsubscribe_stream(stream_name)
                
                self.logger.info("Unsubscribed from order book", symbol=symbol)
                return True
            
            return True
            
        except Exception as e:
            self.logger.error("Failed to unsubscribe from order book", symbol=symbol, error=str(e))
            return False
    
    async def subscribe_trades(self, symbol: str) -> bool:
        """Subscribe to trade updates for a symbol."""
        try:
            binance_symbol = self.denormalize_symbol(symbol)
            stream_name = f"{binance_symbol.lower()}@trade"
            
            if stream_name not in self.stream_names:
                self.stream_names.append(stream_name)
                
                # If WebSocket is connected, subscribe to the stream
                if self.ws_connection:
                    await self._subscribe_stream(stream_name)
                
                self.logger.info("Subscribed to trades", symbol=symbol)
                return True
            
            return True
            
        except Exception as e:
            self.logger.error("Failed to subscribe to trades", symbol=symbol, error=str(e))
            return False
    
    async def unsubscribe_trades(self, symbol: str) -> bool:
        """Unsubscribe from trade updates for a symbol."""
        try:
            binance_symbol = self.denormalize_symbol(symbol)
            stream_name = f"{binance_symbol.lower()}@trade"
            
            if stream_name in self.stream_names:
                self.stream_names.remove(stream_name)
                
                # If WebSocket is connected, unsubscribe from the stream
                if self.ws_connection:
                    await self._unsubscribe_stream(stream_name)
                
                self.logger.info("Unsubscribed from trades", symbol=symbol)
                return True
            
            return True
            
        except Exception as e:
            self.logger.error("Failed to unsubscribe from trades", symbol=symbol, error=str(e))
            return False
    
    async def get_order_book_snapshot(self, symbol: str, depth: int = 20) -> Optional[OrderBook]:
        """Get order book snapshot from REST API."""
        try:
            if not self.session:
                return None
            
            binance_symbol = self.denormalize_symbol(symbol)
            url = f"{self.base_url}/api/v3/depth"
            params = {"symbol": binance_symbol, "limit": depth}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_order_book_snapshot(symbol, data)
                else:
                    self.logger.error("Failed to get order book snapshot", 
                                    symbol=symbol, status=response.status)
                    return None
                    
        except Exception as e:
            self.logger.error("Error getting order book snapshot", symbol=symbol, error=str(e))
            return None
    
    async def get_trading_fees(self, symbol: str) -> Dict[str, float]:
        """Get trading fees for a symbol."""
        # Binance has standard fees, but can be customized per account
        return {
            "maker": self.config.fees.get("maker", 0.001),
            "taker": self.config.fees.get("taker", 0.001)
        }
    
    def normalize_symbol(self, symbol: str) -> str:
        """Convert Binance symbol to normalized format (e.g., BTCUSDT -> BTC/USDT)."""
        # Simple implementation - in production, use a proper symbol mapping
        if "USDT" in symbol:
            base = symbol.replace("USDT", "")
            return f"{base}/USDT"
        elif "BTC" in symbol and symbol != "BTC":
            base = symbol.replace("BTC", "")
            return f"{base}/BTC"
        elif "ETH" in symbol and symbol != "ETH":
            base = symbol.replace("ETH", "")
            return f"{base}/ETH"
        return symbol
    
    def denormalize_symbol(self, symbol: str) -> str:
        """Convert normalized symbol to Binance format (e.g., BTC/USDT -> BTCUSDT)."""
        return symbol.replace("/", "")
    
    async def _test_connectivity(self) -> None:
        """Test REST API connectivity."""
        url = f"{self.base_url}/api/v3/ping"
        async with self.session.get(url) as response:
            if response.status != 200:
                raise Exception(f"Binance API connectivity test failed: {response.status}")
    
    async def _connect_websocket(self) -> None:
        """Connect to Binance WebSocket."""
        try:
            # Create combined stream URL
            if self.stream_names:
                streams = "/".join(self.stream_names)
                ws_url = f"{self.ws_url}/{streams}"
            else:
                # Connect to a dummy stream first
                ws_url = f"{self.ws_url}/btcusdt@ticker"
            
            self.ws_connection = await websockets.connect(ws_url)
            
            # Start WebSocket message handler
            self.ws_task = asyncio.create_task(self._handle_websocket_messages())
            
        except Exception as e:
            self.logger.error("Failed to connect WebSocket", error=str(e))
            raise
    
    async def _handle_websocket_messages(self) -> None:
        """Handle incoming WebSocket messages."""
        try:
            async for message in self.ws_connection:
                try:
                    data = json.loads(message)
                    await self._process_websocket_message(data)
                except json.JSONDecodeError as e:
                    self.logger.error("Failed to parse WebSocket message", error=str(e))
                except Exception as e:
                    self.logger.error("Error processing WebSocket message", error=str(e))
                    
        except websockets.exceptions.ConnectionClosed:
            self.logger.warning("WebSocket connection closed")
            self._update_status(ConnectionStatus.DISCONNECTED)
            # Attempt to reconnect
            await self._reconnect()
        except Exception as e:
            self.logger.error("WebSocket error", error=str(e))
            self._update_status(ConnectionStatus.ERROR)
    
    async def _process_websocket_message(self, data: Dict[str, Any]) -> None:
        """Process WebSocket message."""
        if "stream" in data and "data" in data:
            stream = data["stream"]
            message_data = data["data"]
            
            if "@depth" in stream:
                await self._handle_depth_update(stream, message_data)
            elif "@trade" in stream:
                await self._handle_trade_update(stream, message_data)
        else:
            # Handle single stream message
            if "e" in data:
                event_type = data["e"]
                if event_type == "depthUpdate":
                    await self._handle_depth_update_single(data)
                elif event_type == "trade":
                    await self._handle_trade_update_single(data)
    
    async def _handle_depth_update(self, stream: str, data: Dict[str, Any]) -> None:
        """Handle order book depth update."""
        try:
            # Extract symbol from stream name
            symbol_part = stream.split("@")[0]
            symbol = self.normalize_symbol(symbol_part.upper())
            
            # Parse order book update
            order_book = self._parse_order_book_update(symbol, data)
            if order_book:
                self._notify_order_book_update(order_book)
                
        except Exception as e:
            self.logger.error("Error handling depth update", error=str(e))
    
    async def _handle_depth_update_single(self, data: Dict[str, Any]) -> None:
        """Handle single stream depth update."""
        try:
            symbol = self.normalize_symbol(data["s"])
            order_book = self._parse_order_book_update(symbol, data)
            if order_book:
                self._notify_order_book_update(order_book)
                
        except Exception as e:
            self.logger.error("Error handling single depth update", error=str(e))
    
    async def _handle_trade_update(self, stream: str, data: Dict[str, Any]) -> None:
        """Handle trade update."""
        try:
            # Extract symbol from stream name
            symbol_part = stream.split("@")[0]
            symbol = self.normalize_symbol(symbol_part.upper())
            
            # Parse trade
            trade = self._parse_trade(symbol, data)
            if trade:
                self._notify_trade_update(trade)
                
        except Exception as e:
            self.logger.error("Error handling trade update", error=str(e))
    
    async def _handle_trade_update_single(self, data: Dict[str, Any]) -> None:
        """Handle single stream trade update."""
        try:
            symbol = self.normalize_symbol(data["s"])
            trade = self._parse_trade(symbol, data)
            if trade:
                self._notify_trade_update(trade)
                
        except Exception as e:
            self.logger.error("Error handling single trade update", error=str(e))

    def _parse_order_book_snapshot(self, symbol: str, data: Dict[str, Any]) -> OrderBook:
        """Parse order book snapshot from REST API response."""
        timestamp = time.time()

        bids = [
            OrderBookLevel(price=float(bid[0]), quantity=float(bid[1]), timestamp=timestamp)
            for bid in data.get("bids", [])
        ]

        asks = [
            OrderBookLevel(price=float(ask[0]), quantity=float(ask[1]), timestamp=timestamp)
            for ask in data.get("asks", [])
        ]

        return OrderBook(
            symbol=symbol,
            exchange=self.name,
            bids=bids,
            asks=asks,
            timestamp=timestamp,
            sequence=data.get("lastUpdateId")
        )

    def _parse_order_book_update(self, symbol: str, data: Dict[str, Any]) -> Optional[OrderBook]:
        """Parse order book update from WebSocket message."""
        try:
            timestamp = data.get("E", time.time() * 1000) / 1000  # Convert to seconds

            # Get current order book or create new one
            current_ob = self.get_order_book(symbol)
            if not current_ob:
                # If no current order book, request snapshot
                return None

            # Apply updates
            bids = current_ob.bids.copy()
            asks = current_ob.asks.copy()

            # Update bids
            for bid_update in data.get("b", []):
                price = float(bid_update[0])
                quantity = float(bid_update[1])

                if quantity == 0:
                    # Remove price level
                    bids = [b for b in bids if b.price != price]
                else:
                    # Update or add price level
                    updated = False
                    for i, bid in enumerate(bids):
                        if bid.price == price:
                            bids[i] = OrderBookLevel(price=price, quantity=quantity, timestamp=timestamp)
                            updated = True
                            break

                    if not updated:
                        bids.append(OrderBookLevel(price=price, quantity=quantity, timestamp=timestamp))

            # Update asks
            for ask_update in data.get("a", []):
                price = float(ask_update[0])
                quantity = float(ask_update[1])

                if quantity == 0:
                    # Remove price level
                    asks = [a for a in asks if a.price != price]
                else:
                    # Update or add price level
                    updated = False
                    for i, ask in enumerate(asks):
                        if ask.price == price:
                            asks[i] = OrderBookLevel(price=price, quantity=quantity, timestamp=timestamp)
                            updated = True
                            break

                    if not updated:
                        asks.append(OrderBookLevel(price=price, quantity=quantity, timestamp=timestamp))

            # Sort order book
            bids.sort(key=lambda x: x.price, reverse=True)  # Highest bid first
            asks.sort(key=lambda x: x.price)  # Lowest ask first

            return OrderBook(
                symbol=symbol,
                exchange=self.name,
                bids=bids,
                asks=asks,
                timestamp=timestamp,
                sequence=data.get("u")
            )

        except Exception as e:
            self.logger.error("Error parsing order book update", symbol=symbol, error=str(e))
            return None

    def _parse_trade(self, symbol: str, data: Dict[str, Any]) -> Optional[Trade]:
        """Parse trade from WebSocket message."""
        try:
            return Trade(
                symbol=symbol,
                exchange=self.name,
                price=float(data["p"]),
                quantity=float(data["q"]),
                side="buy" if data["m"] else "sell",  # m=true means buyer is market maker
                timestamp=data.get("T", time.time() * 1000) / 1000,  # Convert to seconds
                trade_id=str(data.get("t"))
            )

        except Exception as e:
            self.logger.error("Error parsing trade", symbol=symbol, error=str(e))
            return None

    async def _subscribe_stream(self, stream_name: str) -> None:
        """Subscribe to a WebSocket stream."""
        # For Binance, we need to reconnect with new streams
        # In production, use the WebSocket API for dynamic subscription
        pass

    async def _unsubscribe_stream(self, stream_name: str) -> None:
        """Unsubscribe from a WebSocket stream."""
        # For Binance, we need to reconnect with updated streams
        # In production, use the WebSocket API for dynamic subscription
        pass

    async def _send_ping(self) -> None:
        """Send ping to WebSocket connection."""
        if self.ws_connection:
            await self.ws_connection.ping()
