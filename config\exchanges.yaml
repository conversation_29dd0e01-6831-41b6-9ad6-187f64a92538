# Exchange Configuration
# Copy this file to exchanges.yaml and add your API credentials

exchanges:
  binance:
    name: "Binance"
    enabled: true
    api_key: "${BINANCE_API_KEY}"
    api_secret: "${BINANCE_API_SECRET}"
    testnet: true
    base_url: "https://api.binance.com"
    ws_url: "wss://stream.binance.com:9443/ws"
    rate_limits:
      requests_per_second: 10
      orders_per_second: 5
      weight_per_minute: 1200
    fees:
      maker: 0.001  # 0.1%
      taker: 0.001  # 0.1%
    supported_pairs:
      - "BTC/USDT"
      - "ETH/USDT"
      - "BNB/USDT"
      - "ADA/USDT"
      - "DOT/USDT"
      - "LINK/USDT"
      - "LTC/USDT"
      - "BCH/USDT"
      - "XRP/USDT"
      - "EOS/USDT"

  coinbase:
    name: "Coinbase Pro"
    enabled: true
    api_key: "${COINBASE_API_KEY}"
    api_secret: "${COINBASE_API_SECRET}"
    passphrase: "${COINBASE_PASSPHRASE}"
    sandbox: true
    base_url: "https://api.pro.coinbase.com"
    ws_url: "wss://ws-feed.pro.coinbase.com"
    rate_limits:
      requests_per_second: 10
      orders_per_second: 5
    fees:
      maker: 0.005  # 0.5%
      taker: 0.005  # 0.5%
    supported_pairs:
      - "BTC-USD"
      - "ETH-USD"
      - "LTC-USD"
      - "BCH-USD"
      - "ADA-USD"
      - "DOT-USD"
      - "LINK-USD"
      - "XRP-USD"

  kraken:
    name: "Kraken"
    enabled: true
    api_key: "${KRAKEN_API_KEY}"
    api_secret: "${KRAKEN_API_SECRET}"
    base_url: "https://api.kraken.com"
    ws_url: "wss://ws.kraken.com"
    rate_limits:
      requests_per_second: 1
      orders_per_second: 1
    fees:
      maker: 0.0016  # 0.16%
      taker: 0.0026  # 0.26%
    supported_pairs:
      - "XBTUSD"
      - "ETHUSD"
      - "LTCUSD"
      - "BCHUSD"
      - "ADAUSD"
      - "DOTUSD"
      - "LINKUSD"
      - "XRPUSD"

  kucoin:
    name: "KuCoin"
    enabled: true
    api_key: "${KUCOIN_API_KEY}"
    api_secret: "${KUCOIN_API_SECRET}"
    passphrase: "${KUCOIN_PASSPHRASE}"
    sandbox: true
    base_url: "https://api.kucoin.com"
    ws_url: "wss://ws-api.kucoin.com/endpoint"
    rate_limits:
      requests_per_second: 10
      orders_per_second: 5
    fees:
      maker: 0.001  # 0.1%
      taker: 0.001  # 0.1%
    supported_pairs:
      - "BTC-USDT"
      - "ETH-USDT"
      - "LTC-USDT"
      - "BCH-USDT"
      - "ADA-USDT"
      - "DOT-USDT"
      - "LINK-USDT"
      - "XRP-USDT"

# Global exchange settings
global:
  connection_timeout: 30
  read_timeout: 10
  max_retries: 3
  retry_delay: 1.0
  heartbeat_interval: 30
  reconnect_delay: 5.0
  max_reconnect_attempts: 10
  
  # Order book settings
  order_book_depth: 20
  order_book_update_interval: 100  # milliseconds
  
  # Data quality settings
  max_price_deviation: 0.1  # 10% max price deviation
  max_spread_threshold: 0.05  # 5% max spread
  stale_data_threshold: 5000  # 5 seconds

# Failover configuration
failover:
  enabled: true
  primary_exchanges: ["binance", "coinbase"]
  backup_exchanges: ["kraken", "kucoin"]
  switch_threshold: 3  # failures before switching
  health_check_interval: 60  # seconds
