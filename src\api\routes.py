"""
REST API routes for ArbitrageVision.

This module provides comprehensive REST API endpoints for accessing
arbitrage opportunities, system status, and configuration.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import time

from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, Field
import structlog

from src.data.models import ArbitrageOpportunity, ArbitrageType, Alert


# Pydantic models for API responses
class OpportunityResponse(BaseModel):
    """Arbitrage opportunity response model."""
    id: str
    type: str
    symbol: str
    buy_exchange: str
    sell_exchange: str
    buy_price: float
    sell_price: float
    quantity: float
    profit_absolute: float
    profit_percentage: float
    timestamp: float
    confidence: float
    risk_score: float = 0.0
    metadata: Dict[str, Any] = {}


class ExchangeStatusResponse(BaseModel):
    """Exchange status response model."""
    name: str
    status: str
    latency: float
    last_update: float
    supported_pairs: int
    message_count: int
    error_count: int


class SystemMetricsResponse(BaseModel):
    """System metrics response model."""
    timestamp: float
    processed_messages: int
    active_connections: int
    opportunities_detected: int
    alerts_generated: int
    system_health_score: float
    components: Dict[str, bool]


class AlertResponse(BaseModel):
    """Alert response model."""
    id: str
    type: str
    severity: str
    title: str
    message: str
    timestamp: float
    acknowledged: bool
    data: Dict[str, Any] = {}


class OrderBookResponse(BaseModel):
    """Order book response model."""
    symbol: str
    exchange: str
    timestamp: float
    best_bid: Optional[float]
    best_ask: Optional[float]
    spread: Optional[float]
    spread_percentage: Optional[float]
    mid_price: Optional[float]


# Create router
router = APIRouter()
logger = structlog.get_logger("APIRoutes")


# Dependency to get application instance
def get_app_instance():
    """Get the application instance from main module."""
    from src.main import app_instance
    if app_instance is None:
        raise HTTPException(status_code=503, detail="Application not initialized")
    return app_instance


@router.get("/opportunities", response_model=List[OpportunityResponse])
async def get_opportunities(
    symbol: Optional[str] = Query(None, description="Filter by symbol"),
    exchange: Optional[str] = Query(None, description="Filter by exchange"),
    min_profit: Optional[float] = Query(None, description="Minimum profit percentage"),
    max_age: Optional[int] = Query(300, description="Maximum age in seconds"),
    limit: Optional[int] = Query(100, description="Maximum number of results"),
    app = Depends(get_app_instance)
):
    """Get current arbitrage opportunities."""
    try:
        opportunities = []
        current_time = time.time()
        
        # Get opportunities from simple arbitrage detector
        if app.simple_detector and app.simple_detector.is_running():
            # This would typically come from a database or cache
            # For now, we'll return a placeholder response
            pass
        
        # Filter opportunities
        filtered_opportunities = []
        for opp in opportunities:
            # Age filter
            if current_time - opp.timestamp > max_age:
                continue
            
            # Symbol filter
            if symbol and opp.symbol != symbol:
                continue
            
            # Exchange filter
            if exchange and exchange not in [opp.buy_exchange, opp.sell_exchange]:
                continue
            
            # Profit filter
            if min_profit and opp.profit_percentage < min_profit:
                continue
            
            filtered_opportunities.append(OpportunityResponse(
                id=opp.id,
                type=opp.type.value,
                symbol=opp.symbol,
                buy_exchange=opp.buy_exchange,
                sell_exchange=opp.sell_exchange,
                buy_price=opp.buy_price,
                sell_price=opp.sell_price,
                quantity=opp.quantity,
                profit_absolute=opp.profit_absolute,
                profit_percentage=opp.profit_percentage,
                timestamp=opp.timestamp,
                confidence=opp.confidence,
                risk_score=opp.risk_score,
                metadata=opp.metadata
            ))
        
        # Limit results
        if limit:
            filtered_opportunities = filtered_opportunities[:limit]
        
        return filtered_opportunities
        
    except Exception as e:
        logger.error("Error getting opportunities", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/opportunities/{opportunity_id}", response_model=OpportunityResponse)
async def get_opportunity(
    opportunity_id: str,
    app = Depends(get_app_instance)
):
    """Get specific arbitrage opportunity by ID."""
    try:
        # This would typically query a database
        # For now, return a 404
        raise HTTPException(status_code=404, detail="Opportunity not found")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error getting opportunity", opportunity_id=opportunity_id, error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/exchanges/status", response_model=Dict[str, ExchangeStatusResponse])
async def get_exchange_status(app = Depends(get_app_instance)):
    """Get status of all exchanges."""
    try:
        if not app.stream_manager:
            raise HTTPException(status_code=503, detail="Stream manager not available")
        
        exchange_status = app.stream_manager.get_exchange_status()
        
        response = {}
        for name, status in exchange_status.items():
            response[name] = ExchangeStatusResponse(
                name=name,
                status=status["status"],
                latency=status["latency"],
                last_update=status["last_update"],
                supported_pairs=status["supported_pairs"],
                message_count=status["message_count"],
                error_count=status["error_count"]
            )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error getting exchange status", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/exchanges/{exchange_name}/orderbook/{symbol}", response_model=OrderBookResponse)
async def get_order_book(
    exchange_name: str,
    symbol: str,
    app = Depends(get_app_instance)
):
    """Get order book for a specific exchange and symbol."""
    try:
        if not app.stream_manager or not app.stream_manager.order_book_manager:
            raise HTTPException(status_code=503, detail="Order book manager not available")
        
        order_book = app.stream_manager.order_book_manager.get_order_book(exchange_name, symbol)
        
        if not order_book:
            raise HTTPException(status_code=404, detail="Order book not found")
        
        return OrderBookResponse(
            symbol=order_book.symbol,
            exchange=order_book.exchange,
            timestamp=order_book.timestamp,
            best_bid=order_book.best_bid.price if order_book.best_bid else None,
            best_ask=order_book.best_ask.price if order_book.best_ask else None,
            spread=order_book.spread,
            spread_percentage=order_book.spread_percentage,
            mid_price=order_book.mid_price
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error getting order book", 
                    exchange=exchange_name, 
                    symbol=symbol, 
                    error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/metrics", response_model=SystemMetricsResponse)
async def get_system_metrics(app = Depends(get_app_instance)):
    """Get system performance metrics."""
    try:
        status = app.get_status()
        
        # Calculate system health score
        component_health = sum(1 for running in status["components"].values() if running)
        total_components = len(status["components"])
        health_score = component_health / total_components if total_components > 0 else 0
        
        return SystemMetricsResponse(
            timestamp=time.time(),
            processed_messages=status["metrics"].get("opportunities_detected", 0),
            active_connections=len([c for c in status["components"].values() if c]),
            opportunities_detected=status["metrics"].get("opportunities_detected", 0),
            alerts_generated=0,  # Would come from notification manager
            system_health_score=health_score,
            components=status["components"]
        )
        
    except Exception as e:
        logger.error("Error getting system metrics", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/alerts", response_model=List[AlertResponse])
async def get_alerts(
    severity: Optional[str] = Query(None, description="Filter by severity"),
    limit: Optional[int] = Query(100, description="Maximum number of results"),
    acknowledged: Optional[bool] = Query(None, description="Filter by acknowledgment status"),
    app = Depends(get_app_instance)
):
    """Get system alerts."""
    try:
        # This would typically come from a database
        # For now, return empty list
        return []
        
    except Exception as e:
        logger.error("Error getting alerts", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/alerts/{alert_id}/acknowledge")
async def acknowledge_alert(
    alert_id: str,
    app = Depends(get_app_instance)
):
    """Acknowledge an alert."""
    try:
        # This would typically update a database
        return {"message": "Alert acknowledged", "alert_id": alert_id}
        
    except Exception as e:
        logger.error("Error acknowledging alert", alert_id=alert_id, error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/statistics")
async def get_statistics(app = Depends(get_app_instance)):
    """Get comprehensive system statistics."""
    try:
        stats = {}
        
        # Stream manager statistics
        if app.stream_manager:
            stats["stream_manager"] = app.stream_manager.get_performance_metrics()
        
        # Simple arbitrage detector statistics
        if app.simple_detector:
            stats["simple_arbitrage"] = app.simple_detector.get_statistics()
        
        # Triangular arbitrage detector statistics
        if app.triangular_detector:
            stats["triangular_arbitrage"] = app.triangular_detector.get_statistics()
        
        # Statistical arbitrage detector statistics
        if app.statistical_detector:
            stats["statistical_arbitrage"] = app.statistical_detector.get_statistics()
        
        # Risk manager statistics
        if app.risk_manager:
            stats["risk_manager"] = app.risk_manager.get_statistics()
        
        # Notification manager statistics
        if app.notification_manager:
            stats["notifications"] = app.notification_manager.get_statistics()
        
        return stats
        
    except Exception as e:
        logger.error("Error getting statistics", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/config")
async def get_configuration(app = Depends(get_app_instance)):
    """Get system configuration."""
    try:
        config = {
            "exchanges": {
                name: {
                    "enabled": config.enabled,
                    "supported_pairs": config.supported_pairs,
                    "fees": config.fees
                }
                for name, config in app.settings.exchanges.items()
            },
            "arbitrage": {
                "simple": {
                    "enabled": app.settings.simple_arbitrage.enabled,
                    "min_profit_threshold": app.settings.simple_arbitrage.min_profit_threshold,
                    "max_position_size": app.settings.simple_arbitrage.max_position_size
                },
                "triangular": {
                    "enabled": app.settings.triangular_arbitrage.enabled,
                    "min_profit_threshold": app.settings.triangular_arbitrage.min_profit_threshold,
                    "max_position_size": app.settings.triangular_arbitrage.max_position_size
                },
                "statistical": {
                    "enabled": app.settings.statistical_arbitrage.enabled,
                    "min_profit_threshold": app.settings.statistical_arbitrage.min_profit_threshold,
                    "max_position_size": app.settings.statistical_arbitrage.max_position_size
                }
            },
            "risk_management": {
                "max_total_exposure": app.settings.risk_management.max_total_exposure,
                "max_exchange_exposure": app.settings.risk_management.max_exchange_exposure,
                "max_daily_drawdown": app.settings.risk_management.max_daily_drawdown
            }
        }
        
        return config
        
    except Exception as e:
        logger.error("Error getting configuration", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/emergency-stop")
async def emergency_stop(app = Depends(get_app_instance)):
    """Trigger emergency stop."""
    try:
        if app.risk_manager:
            app.risk_manager.emergency_stop = True
            logger.warning("Emergency stop triggered via API")
            return {"message": "Emergency stop activated"}
        else:
            raise HTTPException(status_code=503, detail="Risk manager not available")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error triggering emergency stop", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/emergency-stop/reset")
async def reset_emergency_stop(app = Depends(get_app_instance)):
    """Reset emergency stop."""
    try:
        if app.risk_manager:
            app.risk_manager.emergency_stop = False
            app.risk_manager.circuit_breaker_triggered = False
            logger.info("Emergency stop reset via API")
            return {"message": "Emergency stop reset"}
        else:
            raise HTTPException(status_code=503, detail="Risk manager not available")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error resetting emergency stop", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/health/detailed")
async def detailed_health_check(app = Depends(get_app_instance)):
    """Detailed health check with component status."""
    try:
        status = app.get_status()
        
        health_checks = {
            "overall_status": "healthy" if status["running"] else "unhealthy",
            "components": status["components"],
            "exchanges": status["exchanges"],
            "metrics": status["metrics"],
            "timestamp": time.time()
        }
        
        # Determine overall health
        unhealthy_components = [name for name, running in status["components"].items() if not running]
        if unhealthy_components:
            health_checks["overall_status"] = "degraded"
            health_checks["unhealthy_components"] = unhealthy_components
        
        return health_checks
        
    except Exception as e:
        logger.error("Error in detailed health check", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")
