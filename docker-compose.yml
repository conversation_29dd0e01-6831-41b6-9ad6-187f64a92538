version: '3.8'

services:
  # Main Application
  arbitrage-api:
    build: .
    container_name: arbitrage-api
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379
      - INFLUXDB_URL=http://influxdb:8086
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - LOG_LEVEL=INFO
    depends_on:
      - redis
      - influxdb
      - kafka
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - arbitrage-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: arbitrage-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 1gb --maxmemory-policy allkeys-lru
    restart: unless-stopped
    networks:
      - arbitrage-network

  # InfluxDB Time Series Database
  influxdb:
    image: influxdb:2.7-alpine
    container_name: arbitrage-influxdb
    ports:
      - "8086:8086"
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=admin
      - DOCKER_INFLUXDB_INIT_PASSWORD=arbitrage123
      - DOCKER_INFLUXDB_INIT_ORG=arbitrage
      - DOCKER_INFLUXDB_INIT_BUCKET=market_data
      - DOCKER_INFLUXDB_INIT_ADMIN_TOKEN=arbitrage-token-123456789
    volumes:
      - influxdb_data:/var/lib/influxdb2
      - influxdb_config:/etc/influxdb2
    restart: unless-stopped
    networks:
      - arbitrage-network

  # Apache Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: arbitrage-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    networks:
      - arbitrage-network

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: arbitrage-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    volumes:
      - kafka_data:/var/lib/kafka/data
    networks:
      - arbitrage-network

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: arbitrage-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./deployment/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - arbitrage-network

  # Grafana Visualization
  grafana:
    image: grafana/grafana:10.1.0
    container_name: arbitrage-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=arbitrage123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./deployment/monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./deployment/monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    restart: unless-stopped
    networks:
      - arbitrage-network

  # Dashboard Frontend
  dashboard:
    build: ./dashboard
    container_name: arbitrage-dashboard
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000
    depends_on:
      - arbitrage-api
    restart: unless-stopped
    networks:
      - arbitrage-network

volumes:
  redis_data:
  influxdb_data:
  influxdb_config:
  zookeeper_data:
  zookeeper_logs:
  kafka_data:
  prometheus_data:
  grafana_data:

networks:
  arbitrage-network:
    driver: bridge
