"""
Notification system for ArbitrageVision.

This module provides a comprehensive notification system with multiple
channels including webhooks, email, Slack, Telegram, and more.
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import aiohttp
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import structlog

from src.config.settings import Settings
from src.data.models import Alert, ArbitrageOpportunity


class NotificationChannel(Enum):
    """Notification channel types."""
    WEBHOOK = "webhook"
    EMAIL = "email"
    SLACK = "slack"
    TELEGRAM = "telegram"
    DISCORD = "discord"
    SMS = "sms"


@dataclass
class NotificationConfig:
    """Notification channel configuration."""
    channel: NotificationChannel
    enabled: bool
    config: Dict[str, Any]
    rate_limit: int = 60  # Max notifications per minute
    retry_attempts: int = 3
    retry_delay: float = 1.0


@dataclass
class NotificationMessage:
    """Notification message structure."""
    id: str
    channel: NotificationChannel
    title: str
    message: str
    severity: str
    timestamp: float
    data: Dict[str, Any]
    retry_count: int = 0


class NotificationManager:
    """Manages all notification channels and delivery."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = structlog.get_logger("NotificationManager")
        
        # Notification channels
        self.channels: Dict[NotificationChannel, NotificationConfig] = {}
        self._setup_channels()
        
        # Message queue
        self.message_queue: asyncio.Queue = asyncio.Queue()
        
        # Rate limiting
        self.rate_limits: Dict[NotificationChannel, List[float]] = {}
        
        # Statistics
        self.messages_sent: int = 0
        self.messages_failed: int = 0
        self.channel_stats: Dict[NotificationChannel, Dict[str, int]] = {}
        
        # Running state
        self.running: bool = False
        self.worker_tasks: List[asyncio.Task] = []
        
        # HTTP session for webhooks
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def initialize(self) -> None:
        """Initialize the notification manager."""
        self.logger.info("Initializing notification manager")
        
        # Create HTTP session
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30)
        )
        
        # Initialize rate limits
        for channel in self.channels.keys():
            self.rate_limits[channel] = []
            self.channel_stats[channel] = {"sent": 0, "failed": 0}
        
        self.logger.info("Notification manager initialized")
    
    async def start(self) -> None:
        """Start the notification manager."""
        self.logger.info("Starting notification manager")
        
        self.running = True
        
        # Start worker tasks
        for i in range(3):  # 3 worker tasks for parallel processing
            task = asyncio.create_task(self._worker_loop(f"worker-{i}"))
            self.worker_tasks.append(task)
        
        self.logger.info("Notification manager started")
    
    async def stop(self) -> None:
        """Stop the notification manager."""
        self.logger.info("Stopping notification manager")
        
        self.running = False
        
        # Cancel worker tasks
        for task in self.worker_tasks:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
        
        # Close HTTP session
        if self.session:
            await self.session.close()
        
        self.logger.info("Notification manager stopped")
    
    def is_running(self) -> bool:
        """Check if notification manager is running."""
        return self.running
    
    async def send_alert(self, alert: Alert) -> None:
        """Send alert through configured channels."""
        try:
            # Determine which channels to use based on severity
            channels = self._get_channels_for_severity(alert.severity)
            
            for channel in channels:
                if channel in self.channels and self.channels[channel].enabled:
                    # Create notification message
                    message = NotificationMessage(
                        id=f"{alert.id}_{channel.value}",
                        channel=channel,
                        title=alert.title,
                        message=alert.message,
                        severity=alert.severity,
                        timestamp=alert.timestamp,
                        data=alert.data
                    )
                    
                    # Add to queue
                    await self.message_queue.put(message)
                    
        except Exception as e:
            self.logger.error("Error sending alert", alert_id=alert.id, error=str(e))
    
    async def send_opportunity_alert(self, opportunity: ArbitrageOpportunity) -> None:
        """Send arbitrage opportunity alert."""
        try:
            # Determine severity based on profit
            if opportunity.profit_percentage > 2.0:
                severity = "critical"
            elif opportunity.profit_percentage > 1.0:
                severity = "warning"
            else:
                severity = "info"
            
            # Create alert
            alert = Alert(
                id=f"opportunity_{opportunity.id}",
                type="arbitrage_opportunity",
                severity=severity,
                title=f"Arbitrage Opportunity: {opportunity.symbol}",
                message=f"Profit: {opportunity.profit_percentage:.2f}% (${opportunity.profit_absolute:.2f})",
                timestamp=opportunity.timestamp,
                data={
                    "symbol": opportunity.symbol,
                    "buy_exchange": opportunity.buy_exchange,
                    "sell_exchange": opportunity.sell_exchange,
                    "buy_price": opportunity.buy_price,
                    "sell_price": opportunity.sell_price,
                    "quantity": opportunity.quantity,
                    "profit_percentage": opportunity.profit_percentage,
                    "profit_absolute": opportunity.profit_absolute,
                    "confidence": opportunity.confidence,
                    "type": opportunity.type.value
                }
            )
            
            await self.send_alert(alert)
            
        except Exception as e:
            self.logger.error("Error sending opportunity alert", 
                            opportunity_id=opportunity.id, 
                            error=str(e))
    
    def _setup_channels(self) -> None:
        """Setup notification channels from configuration."""
        # Webhook channel
        if hasattr(self.settings.alerts, 'channels'):
            for channel_config in self.settings.alerts.channels:
                channel_type = channel_config.get("type", "").lower()
                
                if channel_type == "webhook" and channel_config.get("enabled", False):
                    self.channels[NotificationChannel.WEBHOOK] = NotificationConfig(
                        channel=NotificationChannel.WEBHOOK,
                        enabled=True,
                        config={
                            "url": channel_config.get("url", ""),
                            "headers": channel_config.get("headers", {}),
                            "method": channel_config.get("method", "POST")
                        }
                    )
                
                elif channel_type == "email" and channel_config.get("enabled", False):
                    self.channels[NotificationChannel.EMAIL] = NotificationConfig(
                        channel=NotificationChannel.EMAIL,
                        enabled=True,
                        config={
                            "smtp_server": channel_config.get("smtp_server", ""),
                            "smtp_port": channel_config.get("smtp_port", 587),
                            "username": channel_config.get("username", ""),
                            "password": channel_config.get("password", ""),
                            "from_email": channel_config.get("from_email", ""),
                            "to_emails": channel_config.get("to_emails", [])
                        }
                    )
                
                elif channel_type == "telegram" and channel_config.get("enabled", False):
                    self.channels[NotificationChannel.TELEGRAM] = NotificationConfig(
                        channel=NotificationChannel.TELEGRAM,
                        enabled=True,
                        config={
                            "bot_token": channel_config.get("bot_token", ""),
                            "chat_id": channel_config.get("chat_id", "")
                        }
                    )
    
    def _get_channels_for_severity(self, severity: str) -> List[NotificationChannel]:
        """Get notification channels based on alert severity."""
        if severity == "critical":
            return list(self.channels.keys())  # All channels for critical alerts
        elif severity == "warning":
            return [NotificationChannel.WEBHOOK, NotificationChannel.TELEGRAM]
        elif severity == "error":
            return [NotificationChannel.WEBHOOK, NotificationChannel.EMAIL]
        else:  # info
            return [NotificationChannel.WEBHOOK]
    
    async def _worker_loop(self, worker_name: str) -> None:
        """Worker loop for processing notification messages."""
        while self.running:
            try:
                # Get message from queue
                message = await asyncio.wait_for(self.message_queue.get(), timeout=1.0)
                
                # Check rate limit
                if not self._check_rate_limit(message.channel):
                    # Re-queue message for later
                    await asyncio.sleep(1)
                    await self.message_queue.put(message)
                    continue
                
                # Send message
                success = await self._send_message(message)
                
                if success:
                    self.messages_sent += 1
                    self.channel_stats[message.channel]["sent"] += 1
                    self.logger.debug("Message sent successfully", 
                                    worker=worker_name,
                                    channel=message.channel.value,
                                    message_id=message.id)
                else:
                    # Retry logic
                    if message.retry_count < self.channels[message.channel].retry_attempts:
                        message.retry_count += 1
                        await asyncio.sleep(self.channels[message.channel].retry_delay)
                        await self.message_queue.put(message)
                    else:
                        self.messages_failed += 1
                        self.channel_stats[message.channel]["failed"] += 1
                        self.logger.error("Message failed after retries", 
                                        worker=worker_name,
                                        channel=message.channel.value,
                                        message_id=message.id)
                
            except asyncio.TimeoutError:
                continue
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Error in worker loop", worker=worker_name, error=str(e))
    
    def _check_rate_limit(self, channel: NotificationChannel) -> bool:
        """Check if channel is within rate limit."""
        current_time = time.time()
        channel_times = self.rate_limits[channel]
        
        # Remove old timestamps (older than 1 minute)
        channel_times[:] = [t for t in channel_times if current_time - t < 60]
        
        # Check if we can send
        config = self.channels[channel]
        if len(channel_times) < config.rate_limit:
            channel_times.append(current_time)
            return True
        
        return False
    
    async def _send_message(self, message: NotificationMessage) -> bool:
        """Send message through specific channel."""
        try:
            if message.channel == NotificationChannel.WEBHOOK:
                return await self._send_webhook(message)
            elif message.channel == NotificationChannel.EMAIL:
                return await self._send_email(message)
            elif message.channel == NotificationChannel.TELEGRAM:
                return await self._send_telegram(message)
            else:
                self.logger.warning("Unsupported notification channel", channel=message.channel.value)
                return False
                
        except Exception as e:
            self.logger.error("Error sending message", 
                            channel=message.channel.value,
                            message_id=message.id,
                            error=str(e))
            return False
    
    async def _send_webhook(self, message: NotificationMessage) -> bool:
        """Send webhook notification."""
        try:
            config = self.channels[NotificationChannel.WEBHOOK].config
            
            payload = {
                "id": message.id,
                "title": message.title,
                "message": message.message,
                "severity": message.severity,
                "timestamp": message.timestamp,
                "data": message.data
            }
            
            headers = config.get("headers", {})
            headers["Content-Type"] = "application/json"
            
            async with self.session.request(
                method=config.get("method", "POST"),
                url=config["url"],
                json=payload,
                headers=headers
            ) as response:
                return response.status < 400
                
        except Exception as e:
            self.logger.error("Error sending webhook", error=str(e))
            return False
    
    async def _send_email(self, message: NotificationMessage) -> bool:
        """Send email notification."""
        try:
            config = self.channels[NotificationChannel.EMAIL].config
            
            # Create message
            msg = MIMEMultipart()
            msg["From"] = config["from_email"]
            msg["To"] = ", ".join(config["to_emails"])
            msg["Subject"] = f"[ArbitrageVision] {message.title}"
            
            # Email body
            body = f"""
{message.message}

Severity: {message.severity.upper()}
Time: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(message.timestamp))}

Additional Data:
{json.dumps(message.data, indent=2)}

---
ArbitrageVision Alert System
            """
            
            msg.attach(MIMEText(body, "plain"))
            
            # Send email
            server = smtplib.SMTP(config["smtp_server"], config["smtp_port"])
            server.starttls()
            server.login(config["username"], config["password"])
            server.send_message(msg)
            server.quit()
            
            return True
            
        except Exception as e:
            self.logger.error("Error sending email", error=str(e))
            return False
    
    async def _send_telegram(self, message: NotificationMessage) -> bool:
        """Send Telegram notification."""
        try:
            config = self.channels[NotificationChannel.TELEGRAM].config
            
            # Format message
            text = f"🚨 *{message.title}*\n\n{message.message}\n\n"
            text += f"Severity: {message.severity.upper()}\n"
            text += f"Time: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(message.timestamp))}"
            
            # Add key data
            if message.data:
                text += "\n\n📊 *Details:*\n"
                for key, value in message.data.items():
                    if isinstance(value, (int, float)):
                        text += f"• {key}: {value}\n"
                    elif isinstance(value, str) and len(value) < 50:
                        text += f"• {key}: {value}\n"
            
            url = f"https://api.telegram.org/bot{config['bot_token']}/sendMessage"
            payload = {
                "chat_id": config["chat_id"],
                "text": text,
                "parse_mode": "Markdown"
            }
            
            async with self.session.post(url, json=payload) as response:
                return response.status < 400
                
        except Exception as e:
            self.logger.error("Error sending Telegram message", error=str(e))
            return False
    
    def get_statistics(self) -> Dict:
        """Get notification manager statistics."""
        return {
            "messages_sent": self.messages_sent,
            "messages_failed": self.messages_failed,
            "queue_size": self.message_queue.qsize(),
            "enabled_channels": len([c for c in self.channels.values() if c.enabled]),
            "channel_stats": {
                channel.value: stats for channel, stats in self.channel_stats.items()
            },
            "success_rate": (self.messages_sent / max(self.messages_sent + self.messages_failed, 1)) * 100
        }
