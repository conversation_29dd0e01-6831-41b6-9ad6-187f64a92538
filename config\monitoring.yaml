# Monitoring and Observability Configuration

# Prometheus Metrics Configuration
prometheus:
  enabled: true
  port: 8001
  path: "/metrics"
  
  # Custom metrics
  custom_metrics:
    - name: "arbitrage_opportunities_total"
      type: "counter"
      description: "Total number of arbitrage opportunities detected"
      labels: ["exchange_pair", "strategy", "profit_tier"]
    
    - name: "arbitrage_profit_usd"
      type: "histogram"
      description: "Profit amount in USD for executed arbitrage"
      buckets: [1, 5, 10, 25, 50, 100, 250, 500, 1000]
      labels: ["exchange_pair", "strategy"]
    
    - name: "order_book_update_latency_ms"
      type: "histogram"
      description: "Order book update latency in milliseconds"
      buckets: [0.1, 0.5, 1, 2, 5, 10, 25, 50, 100]
      labels: ["exchange"]
    
    - name: "exchange_connection_status"
      type: "gauge"
      description: "Exchange connection status (1=connected, 0=disconnected)"
      labels: ["exchange", "connection_type"]
    
    - name: "risk_score"
      type: "gauge"
      description: "Current risk score (0-1)"
      labels: ["risk_type"]
    
    - name: "system_memory_usage_bytes"
      type: "gauge"
      description: "System memory usage in bytes"
    
    - name: "active_positions_count"
      type: "gauge"
      description: "Number of active positions"
      labels: ["exchange", "strategy"]

# Grafana Dashboard Configuration
grafana:
  enabled: true
  dashboards:
    - name: "Arbitrage Overview"
      file: "arbitrage_overview.json"
      folder: "ArbitrageVision"
    
    - name: "Exchange Health"
      file: "exchange_health.json"
      folder: "ArbitrageVision"
    
    - name: "Risk Management"
      file: "risk_management.json"
      folder: "ArbitrageVision"
    
    - name: "Performance Metrics"
      file: "performance_metrics.json"
      folder: "ArbitrageVision"
    
    - name: "System Health"
      file: "system_health.json"
      folder: "ArbitrageVision"

# Logging Configuration
logging:
  level: "INFO"
  format: "json"
  
  # Log files
  files:
    main: "logs/arbitrage.log"
    error: "logs/error.log"
    audit: "logs/audit.log"
    performance: "logs/performance.log"
  
  # Log rotation
  rotation:
    max_size: "100MB"
    backup_count: 10
    when: "midnight"
  
  # Structured logging fields
  fields:
    - "timestamp"
    - "level"
    - "logger"
    - "message"
    - "exchange"
    - "trading_pair"
    - "strategy"
    - "profit"
    - "risk_score"
    - "latency_ms"
    - "correlation_id"

# Health Checks
health_checks:
  enabled: true
  interval: 30  # seconds
  timeout: 10   # seconds
  
  checks:
    - name: "database_connection"
      type: "database"
      critical: true
    
    - name: "redis_connection"
      type: "redis"
      critical: true
    
    - name: "exchange_connections"
      type: "exchange"
      critical: true
    
    - name: "kafka_connection"
      type: "kafka"
      critical: false
    
    - name: "memory_usage"
      type: "system"
      critical: false
      threshold: 0.9  # 90% memory usage
    
    - name: "disk_usage"
      type: "system"
      critical: false
      threshold: 0.85  # 85% disk usage

# Alerting Rules
alerting:
  enabled: true
  
  # Alert channels
  channels:
    slack:
      enabled: false
      webhook_url: "${SLACK_WEBHOOK_URL}"
      channel: "#arbitrage-alerts"
    
    email:
      enabled: false
      smtp_server: "${SMTP_SERVER}"
      smtp_port: 587
      username: "${SMTP_USERNAME}"
      password: "${SMTP_PASSWORD}"
      from_email: "<EMAIL>"
      to_emails: ["<EMAIL>"]
    
    webhook:
      enabled: true
      url: "${ALERT_WEBHOOK_URL}"
      timeout: 10
  
  # Alert rules
  rules:
    - name: "High Profit Opportunity"
      condition: "arbitrage_profit_usd > 100"
      severity: "info"
      message: "High profit arbitrage opportunity detected: ${profit}USD"
    
    - name: "Exchange Connection Lost"
      condition: "exchange_connection_status == 0"
      severity: "critical"
      message: "Exchange connection lost: ${exchange}"
    
    - name: "High Latency"
      condition: "order_book_update_latency_ms > 50"
      severity: "warning"
      message: "High latency detected: ${latency}ms on ${exchange}"
    
    - name: "High Risk Score"
      condition: "risk_score > 0.8"
      severity: "warning"
      message: "High risk score detected: ${risk_score}"
    
    - name: "Memory Usage High"
      condition: "system_memory_usage_bytes / system_memory_total_bytes > 0.9"
      severity: "warning"
      message: "High memory usage: ${usage}%"
    
    - name: "System Error Rate High"
      condition: "rate(error_total[5m]) > 0.1"
      severity: "critical"
      message: "High error rate detected: ${rate} errors/second"

# Performance Monitoring
performance:
  enabled: true
  
  # Benchmarks
  benchmarks:
    order_book_update_target: 1    # milliseconds
    arbitrage_detection_target: 5  # milliseconds
    alert_generation_target: 10    # milliseconds
    api_response_target: 100       # milliseconds
  
  # Profiling
  profiling:
    enabled: false  # Enable only for debugging
    interval: 300   # seconds
    duration: 60    # seconds
    output_dir: "logs/profiles"
  
  # Load testing
  load_testing:
    enabled: false
    concurrent_users: 100
    ramp_up_time: 60  # seconds
    test_duration: 300  # seconds

# Data Retention
data_retention:
  # Time series data
  influxdb:
    raw_data: "7d"      # 7 days
    aggregated_1m: "30d" # 30 days
    aggregated_1h: "90d" # 90 days
    aggregated_1d: "1y"  # 1 year
  
  # Log files
  logs:
    application: "30d"
    error: "90d"
    audit: "1y"
    performance: "30d"
  
  # Cache data
  redis:
    order_books: "1h"
    market_data: "24h"
    user_sessions: "7d"

# Security Monitoring
security:
  enabled: true
  
  # Rate limiting monitoring
  rate_limiting:
    monitor_failed_attempts: true
    alert_threshold: 100  # failed attempts per minute
  
  # API security
  api_security:
    monitor_unauthorized_access: true
    monitor_suspicious_patterns: true
    log_all_requests: false  # Enable only for debugging
  
  # Data integrity
  data_integrity:
    checksum_validation: true
    anomaly_detection: true
    corruption_alerts: true
