"""
Profitability analysis for ArbitrageVision.

This module provides comprehensive profitability analysis including
fee calculation, slippage estimation, and execution cost modeling.
"""

import time
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import structlog

from src.config.settings import Settings
from src.data.models import ArbitrageOpportunity, OrderBookSnapshot, Position
from src.exchanges.base import BaseExchange


@dataclass
class TradingCosts:
    """Trading costs breakdown."""
    maker_fee: float
    taker_fee: float
    slippage: float
    network_fee: float
    total_cost: float
    total_cost_percentage: float


@dataclass
class ProfitabilityAnalysis:
    """Profitability analysis result."""
    gross_profit: float
    net_profit: float
    trading_costs: TradingCosts
    execution_time_estimate: float
    risk_adjusted_profit: float
    profit_probability: float
    expected_value: float
    roi_percentage: float
    is_profitable: bool


class ProfitabilityAnalyzer:
    """Analyzes profitability of arbitrage opportunities."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = structlog.get_logger("ProfitabilityAnalyzer")
        
        # Exchange fee cache
        self.exchange_fees: Dict[str, Dict[str, float]] = {}
        
        # Slippage models
        self.slippage_models: Dict[str, Dict] = {}
        
        # Network latency measurements
        self.network_latencies: Dict[str, float] = {}
        
        # Historical execution data
        self.execution_history: List[Dict] = []
        
    async def analyze_opportunity(self, 
                                opportunity: ArbitrageOpportunity,
                                buy_order_book: Optional[OrderBookSnapshot] = None,
                                sell_order_book: Optional[OrderBookSnapshot] = None) -> ProfitabilityAnalysis:
        """Analyze profitability of an arbitrage opportunity."""
        try:
            # Calculate trading costs
            trading_costs = await self._calculate_trading_costs(
                opportunity, buy_order_book, sell_order_book
            )
            
            # Calculate net profit
            gross_profit = opportunity.profit_absolute
            net_profit = gross_profit - trading_costs.total_cost
            
            # Estimate execution time
            execution_time = self._estimate_execution_time(opportunity)
            
            # Calculate risk-adjusted profit
            risk_adjusted_profit = self._calculate_risk_adjusted_profit(
                net_profit, opportunity, execution_time
            )
            
            # Calculate profit probability
            profit_probability = self._calculate_profit_probability(
                opportunity, trading_costs, execution_time
            )
            
            # Calculate expected value
            expected_value = net_profit * profit_probability
            
            # Calculate ROI
            investment = opportunity.buy_price * opportunity.quantity
            roi_percentage = (net_profit / investment) * 100 if investment > 0 else 0
            
            # Determine if profitable
            is_profitable = (
                net_profit > 0 and
                profit_probability > 0.7 and
                roi_percentage > self.settings.simple_arbitrage.min_profit_threshold * 100
            )
            
            return ProfitabilityAnalysis(
                gross_profit=gross_profit,
                net_profit=net_profit,
                trading_costs=trading_costs,
                execution_time_estimate=execution_time,
                risk_adjusted_profit=risk_adjusted_profit,
                profit_probability=profit_probability,
                expected_value=expected_value,
                roi_percentage=roi_percentage,
                is_profitable=is_profitable
            )
            
        except Exception as e:
            self.logger.error("Error analyzing opportunity profitability", 
                            opportunity_id=opportunity.id, 
                            error=str(e))
            
            # Return default analysis
            return ProfitabilityAnalysis(
                gross_profit=0,
                net_profit=0,
                trading_costs=TradingCosts(0, 0, 0, 0, 0, 0),
                execution_time_estimate=0,
                risk_adjusted_profit=0,
                profit_probability=0,
                expected_value=0,
                roi_percentage=0,
                is_profitable=False
            )
    
    async def _calculate_trading_costs(self, 
                                     opportunity: ArbitrageOpportunity,
                                     buy_order_book: Optional[OrderBookSnapshot],
                                     sell_order_book: Optional[OrderBookSnapshot]) -> TradingCosts:
        """Calculate comprehensive trading costs."""
        try:
            # Get exchange fees
            buy_fees = await self._get_exchange_fees(opportunity.buy_exchange, opportunity.symbol)
            sell_fees = await self._get_exchange_fees(opportunity.sell_exchange, opportunity.symbol)
            
            # Calculate fee costs
            buy_value = opportunity.buy_price * opportunity.quantity
            sell_value = opportunity.sell_price * opportunity.quantity
            
            # Assume taker fees for arbitrage (immediate execution)
            buy_fee_cost = buy_value * buy_fees.get("taker", 0.001)
            sell_fee_cost = sell_value * sell_fees.get("taker", 0.001)
            
            total_fee_cost = buy_fee_cost + sell_fee_cost
            
            # Calculate slippage
            buy_slippage = self._calculate_slippage(
                opportunity.buy_exchange, 
                opportunity.symbol, 
                opportunity.quantity, 
                "buy",
                buy_order_book
            )
            
            sell_slippage = self._calculate_slippage(
                opportunity.sell_exchange, 
                opportunity.symbol, 
                opportunity.quantity, 
                "sell",
                sell_order_book
            )
            
            total_slippage = buy_slippage + sell_slippage
            
            # Network/gas fees (simplified)
            network_fee = self._estimate_network_fees(opportunity)
            
            # Total costs
            total_cost = total_fee_cost + total_slippage + network_fee
            total_cost_percentage = (total_cost / buy_value) * 100 if buy_value > 0 else 0
            
            return TradingCosts(
                maker_fee=buy_fees.get("maker", 0.001) + sell_fees.get("maker", 0.001),
                taker_fee=buy_fees.get("taker", 0.001) + sell_fees.get("taker", 0.001),
                slippage=total_slippage,
                network_fee=network_fee,
                total_cost=total_cost,
                total_cost_percentage=total_cost_percentage
            )
            
        except Exception as e:
            self.logger.error("Error calculating trading costs", error=str(e))
            return TradingCosts(0, 0, 0, 0, 0, 0)
    
    async def _get_exchange_fees(self, exchange: str, symbol: str) -> Dict[str, float]:
        """Get exchange fees with caching."""
        cache_key = f"{exchange}:{symbol}"
        
        if cache_key in self.exchange_fees:
            return self.exchange_fees[cache_key]
        
        # Get fees from configuration or exchange
        exchange_config = self.settings.get_exchange_config(exchange)
        if exchange_config:
            fees = exchange_config.fees
            self.exchange_fees[cache_key] = fees
            return fees
        
        # Default fees
        default_fees = {"maker": 0.001, "taker": 0.001}
        self.exchange_fees[cache_key] = default_fees
        return default_fees
    
    def _calculate_slippage(self, 
                          exchange: str, 
                          symbol: str, 
                          quantity: float, 
                          side: str,
                          order_book: Optional[OrderBookSnapshot]) -> float:
        """Calculate slippage for a trade."""
        try:
            if not order_book:
                # Use historical slippage model
                return self._estimate_slippage_from_model(exchange, symbol, quantity, side)
            
            # Calculate slippage from order book
            levels = order_book.asks if side == "buy" else order_book.bids
            
            if not levels:
                return quantity * 0.001  # 0.1% default slippage
            
            remaining_quantity = quantity
            total_cost = 0
            best_price = levels[0].price
            
            for level in levels:
                if remaining_quantity <= 0:
                    break
                
                level_quantity = min(level.quantity, remaining_quantity)
                total_cost += level_quantity * level.price
                remaining_quantity -= level_quantity
            
            if remaining_quantity > 0:
                # Not enough liquidity - high slippage
                return quantity * 0.01  # 1% slippage penalty
            
            # Calculate average execution price
            avg_price = total_cost / quantity
            slippage_percentage = abs(avg_price - best_price) / best_price
            
            return quantity * best_price * slippage_percentage
            
        except Exception as e:
            self.logger.error("Error calculating slippage", error=str(e))
            return quantity * 0.001  # Default slippage
    
    def _estimate_slippage_from_model(self, exchange: str, symbol: str, quantity: float, side: str) -> float:
        """Estimate slippage using historical model."""
        # Simplified slippage model based on quantity
        base_slippage = 0.0005  # 0.05%
        
        # Adjust for quantity (larger trades have more slippage)
        quantity_factor = min(quantity / 1000, 2.0)  # Cap at 2x
        
        # Adjust for exchange (some exchanges have better liquidity)
        exchange_factor = {
            "binance": 0.8,
            "coinbase": 1.0,
            "kraken": 1.2,
            "kucoin": 1.1
        }.get(exchange.lower(), 1.0)
        
        slippage_rate = base_slippage * quantity_factor * exchange_factor
        return quantity * slippage_rate
    
    def _estimate_network_fees(self, opportunity: ArbitrageOpportunity) -> float:
        """Estimate network/gas fees."""
        # Simplified network fee estimation
        # In practice, this would depend on the blockchain and current gas prices
        
        if opportunity.type.value == "simple":
            return 0.5  # $0.50 per trade
        elif opportunity.type.value == "triangular":
            return 1.5  # $1.50 for multiple trades
        else:
            return 1.0  # $1.00 default
    
    def _estimate_execution_time(self, opportunity: ArbitrageOpportunity) -> float:
        """Estimate execution time in seconds."""
        base_time = 0.5  # 500ms base execution time
        
        # Add network latency
        buy_latency = self.network_latencies.get(opportunity.buy_exchange, 0.1)
        sell_latency = self.network_latencies.get(opportunity.sell_exchange, 0.1)
        
        # Add complexity factor
        complexity_factor = {
            "simple": 1.0,
            "triangular": 2.0,
            "statistical": 1.5
        }.get(opportunity.type.value, 1.0)
        
        return base_time + buy_latency + sell_latency * complexity_factor
    
    def _calculate_risk_adjusted_profit(self, 
                                      net_profit: float, 
                                      opportunity: ArbitrageOpportunity, 
                                      execution_time: float) -> float:
        """Calculate risk-adjusted profit."""
        # Risk factors
        risk_factors = []
        
        # Time risk (longer execution = higher risk)
        time_risk = min(execution_time / 5.0, 1.0)  # 5 seconds as reference
        risk_factors.append(time_risk)
        
        # Confidence risk
        confidence_risk = 1.0 - opportunity.confidence
        risk_factors.append(confidence_risk)
        
        # Exchange risk (different exchanges have different risks)
        exchange_risk = self._get_exchange_risk_factor(opportunity.buy_exchange, opportunity.sell_exchange)
        risk_factors.append(exchange_risk)
        
        # Calculate overall risk factor
        overall_risk = sum(risk_factors) / len(risk_factors)
        
        # Apply risk adjustment
        risk_adjustment = 1.0 - (overall_risk * 0.5)  # Max 50% risk adjustment
        
        return net_profit * risk_adjustment
    
    def _get_exchange_risk_factor(self, buy_exchange: str, sell_exchange: str) -> float:
        """Get risk factor for exchange combination."""
        # Risk scores for exchanges (lower is better)
        exchange_risks = {
            "binance": 0.1,
            "coinbase": 0.15,
            "kraken": 0.2,
            "kucoin": 0.25
        }
        
        buy_risk = exchange_risks.get(buy_exchange.lower(), 0.3)
        sell_risk = exchange_risks.get(sell_exchange.lower(), 0.3)
        
        return (buy_risk + sell_risk) / 2
    
    def _calculate_profit_probability(self, 
                                    opportunity: ArbitrageOpportunity, 
                                    trading_costs: TradingCosts, 
                                    execution_time: float) -> float:
        """Calculate probability of profitable execution."""
        probability_factors = []
        
        # Profit margin factor (higher margin = higher probability)
        profit_margin = opportunity.profit_percentage
        margin_factor = min(profit_margin / 1.0, 1.0)  # 1% as reference
        probability_factors.append(margin_factor)
        
        # Confidence factor
        probability_factors.append(opportunity.confidence)
        
        # Cost factor (lower costs = higher probability)
        cost_factor = max(0, 1.0 - (trading_costs.total_cost_percentage / 2.0))  # 2% as reference
        probability_factors.append(cost_factor)
        
        # Time factor (faster execution = higher probability)
        time_factor = max(0, 1.0 - (execution_time / 10.0))  # 10 seconds as reference
        probability_factors.append(time_factor)
        
        # Market volatility factor (simplified)
        volatility_factor = 0.8  # Assume moderate volatility
        probability_factors.append(volatility_factor)
        
        return sum(probability_factors) / len(probability_factors)
    
    def update_network_latency(self, exchange: str, latency: float) -> None:
        """Update network latency measurement for an exchange."""
        self.network_latencies[exchange] = latency
    
    def record_execution(self, opportunity_id: str, actual_profit: float, execution_time: float) -> None:
        """Record actual execution results for model improvement."""
        self.execution_history.append({
            "opportunity_id": opportunity_id,
            "actual_profit": actual_profit,
            "execution_time": execution_time,
            "timestamp": time.time()
        })
        
        # Keep only recent history
        if len(self.execution_history) > 1000:
            self.execution_history = self.execution_history[-1000:]
    
    def get_performance_metrics(self) -> Dict:
        """Get profitability analyzer performance metrics."""
        if not self.execution_history:
            return {}
        
        recent_executions = [
            ex for ex in self.execution_history 
            if time.time() - ex["timestamp"] < 3600  # Last hour
        ]
        
        if not recent_executions:
            return {}
        
        total_profit = sum(ex["actual_profit"] for ex in recent_executions)
        avg_profit = total_profit / len(recent_executions)
        avg_execution_time = sum(ex["execution_time"] for ex in recent_executions) / len(recent_executions)
        
        profitable_count = len([ex for ex in recent_executions if ex["actual_profit"] > 0])
        success_rate = profitable_count / len(recent_executions)
        
        return {
            "total_executions": len(recent_executions),
            "total_profit": total_profit,
            "average_profit": avg_profit,
            "average_execution_time": avg_execution_time,
            "success_rate": success_rate,
            "cached_fees": len(self.exchange_fees),
            "network_latencies": dict(self.network_latencies)
        }
