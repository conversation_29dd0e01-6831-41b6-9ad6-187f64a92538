"""
Simple arbitrage detection for ArbitrageVision.

This module implements simple arbitrage detection by comparing prices
across different exchanges for the same trading pair.
"""

import asyncio
import time
import uuid
from typing import Dict, List, Optional, Callable, Set
from collections import defaultdict
import structlog

from src.config.settings import Settings
from src.data.models import ArbitrageOpportunity, ArbitrageType, OrderBookSnapshot
from src.exchanges.base import OrderBook


class SimpleArbitrageDetector:
    """Simple arbitrage opportunity detector."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.config = settings.simple_arbitrage
        self.logger = structlog.get_logger("SimpleArbitrageDetector")
        
        # Current order books by exchange and symbol
        self.order_books: Dict[str, Dict[str, OrderBookSnapshot]] = defaultdict(dict)
        
        # Opportunity handlers
        self.opportunity_handlers: List[Callable[[ArbitrageOpportunity], None]] = []
        
        # Performance tracking
        self.opportunities_found = 0
        self.last_scan_time = 0
        self.scan_count = 0
        
        # Running state
        self.running = False
        self.scan_task: Optional[asyncio.Task] = None
        
        # Cache for recent opportunities to avoid duplicates
        self.recent_opportunities: Dict[str, float] = {}
        self.opportunity_cache_ttl = 5.0  # seconds
        
    async def start(self) -> None:
        """Start the simple arbitrage detector."""
        self.logger.info("Starting simple arbitrage detector")
        
        self.running = True
        
        # Start scanning task
        self.scan_task = asyncio.create_task(self._scan_loop())
        
        self.logger.info("Simple arbitrage detector started")
    
    async def stop(self) -> None:
        """Stop the simple arbitrage detector."""
        self.logger.info("Stopping simple arbitrage detector")
        
        self.running = False
        
        if self.scan_task:
            self.scan_task.cancel()
            try:
                await self.scan_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Simple arbitrage detector stopped")
    
    def is_running(self) -> bool:
        """Check if detector is running."""
        return self.running
    
    def add_opportunity_handler(self, handler: Callable[[ArbitrageOpportunity], None]) -> None:
        """Add opportunity handler."""
        self.opportunity_handlers.append(handler)
    
    def remove_opportunity_handler(self, handler: Callable[[ArbitrageOpportunity], None]) -> None:
        """Remove opportunity handler."""
        if handler in self.opportunity_handlers:
            self.opportunity_handlers.remove(handler)
    
    async def process_update(self, order_book: OrderBookSnapshot) -> None:
        """Process order book update."""
        try:
            # Store order book
            self.order_books[order_book.exchange][order_book.symbol] = order_book
            
            # Check for arbitrage opportunities for this symbol
            await self._check_arbitrage_for_symbol(order_book.symbol)
            
        except Exception as e:
            self.logger.error("Error processing order book update", 
                            exchange=order_book.exchange,
                            symbol=order_book.symbol,
                            error=str(e))
    
    async def _scan_loop(self) -> None:
        """Main scanning loop."""
        while self.running:
            try:
                start_time = time.perf_counter()
                
                # Scan all configured symbols
                for symbol in self.config.trading_pairs:
                    await self._check_arbitrage_for_symbol(symbol)
                
                # Clean up old opportunities from cache
                self._cleanup_opportunity_cache()
                
                # Track performance
                scan_time = (time.perf_counter() - start_time) * 1000
                self.last_scan_time = scan_time
                self.scan_count += 1
                
                if scan_time > 10:  # Log if scan takes more than 10ms
                    self.logger.warning("Slow arbitrage scan", scan_time_ms=scan_time)
                
                # Wait for next scan
                await asyncio.sleep(self.config.update_frequency / 1000.0)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Error in scan loop", error=str(e))
                await asyncio.sleep(1)  # Prevent tight error loop
    
    async def _check_arbitrage_for_symbol(self, symbol: str) -> None:
        """Check for arbitrage opportunities for a specific symbol."""
        try:
            # Get all order books for this symbol
            symbol_books = {}
            for exchange, books in self.order_books.items():
                if symbol in books:
                    book = books[symbol]
                    # Check if order book is fresh enough
                    if time.time() - book.timestamp <= self.config.max_spread_age / 1000.0:
                        symbol_books[exchange] = book
            
            if len(symbol_books) < 2:
                return  # Need at least 2 exchanges
            
            # Find arbitrage opportunities
            opportunities = self._find_opportunities(symbol, symbol_books)
            
            # Process opportunities
            for opportunity in opportunities:
                await self._handle_opportunity(opportunity)
                
        except Exception as e:
            self.logger.error("Error checking arbitrage for symbol", symbol=symbol, error=str(e))
    
    def _find_opportunities(self, symbol: str, order_books: Dict[str, OrderBookSnapshot]) -> List[ArbitrageOpportunity]:
        """Find arbitrage opportunities for a symbol."""
        opportunities = []
        exchanges = list(order_books.keys())
        
        for i, buy_exchange in enumerate(exchanges):
            for j, sell_exchange in enumerate(exchanges):
                if i >= j:
                    continue
                
                buy_book = order_books[buy_exchange]
                sell_book = order_books[sell_exchange]
                
                # Check if we have valid prices
                if not (buy_book.best_ask and sell_book.best_bid):
                    continue
                
                buy_price = buy_book.best_ask.price
                sell_price = sell_book.best_bid.price
                
                # Check if there's a profitable opportunity
                if sell_price > buy_price:
                    # Calculate potential quantity
                    max_buy_quantity = buy_book.best_ask.quantity
                    max_sell_quantity = sell_book.best_bid.quantity
                    quantity = min(max_buy_quantity, max_sell_quantity)
                    
                    # Apply position size limits
                    max_position_value = self.config.max_position_size
                    max_quantity_by_value = max_position_value / buy_price
                    quantity = min(quantity, max_quantity_by_value)
                    
                    if quantity <= 0:
                        continue
                    
                    # Calculate profit
                    profit_absolute = (sell_price - buy_price) * quantity
                    profit_percentage = (profit_absolute / (buy_price * quantity)) * 100
                    
                    # Check minimum profit threshold
                    if profit_percentage < self.config.min_profit_threshold * 100:
                        continue
                    
                    # Check maximum profit threshold (likely error)
                    if profit_percentage > self.config.max_profit_threshold * 100:
                        self.logger.warning("Suspiciously high profit opportunity",
                                          symbol=symbol,
                                          buy_exchange=buy_exchange,
                                          sell_exchange=sell_exchange,
                                          profit_percentage=profit_percentage)
                        continue
                    
                    # Check minimum volume threshold
                    if profit_absolute < self.config.min_volume_threshold:
                        continue
                    
                    # Check order book depth
                    if not self._check_liquidity(buy_book, sell_book, quantity):
                        continue
                    
                    # Create opportunity
                    opportunity = ArbitrageOpportunity(
                        id=str(uuid.uuid4()),
                        type=ArbitrageType.SIMPLE,
                        symbol=symbol,
                        buy_exchange=buy_exchange,
                        sell_exchange=sell_exchange,
                        buy_price=buy_price,
                        sell_price=sell_price,
                        quantity=quantity,
                        profit_absolute=profit_absolute,
                        profit_percentage=profit_percentage,
                        timestamp=time.time(),
                        confidence=self._calculate_confidence(buy_book, sell_book),
                        metadata={
                            "buy_book_depth": len(buy_book.bids),
                            "sell_book_depth": len(sell_book.asks),
                            "buy_spread": buy_book.spread_percentage,
                            "sell_spread": sell_book.spread_percentage,
                            "age_difference": abs(buy_book.timestamp - sell_book.timestamp)
                        }
                    )
                    
                    opportunities.append(opportunity)
        
        # Sort by profit percentage (highest first)
        opportunities.sort(key=lambda x: x.profit_percentage, reverse=True)
        
        return opportunities
    
    def _check_liquidity(self, buy_book: OrderBookSnapshot, sell_book: OrderBookSnapshot, quantity: float) -> bool:
        """Check if there's sufficient liquidity for the trade."""
        # Check minimum depth requirement
        if len(buy_book.asks) < self.config.min_liquidity_depth:
            return False
        
        if len(sell_book.bids) < self.config.min_liquidity_depth:
            return False
        
        # Check if we can execute the full quantity
        buy_available = sum(level.quantity for level in buy_book.asks[:5])
        sell_available = sum(level.quantity for level in sell_book.bids[:5])
        
        return buy_available >= quantity and sell_available >= quantity
    
    def _calculate_confidence(self, buy_book: OrderBookSnapshot, sell_book: OrderBookSnapshot) -> float:
        """Calculate confidence score for the opportunity."""
        confidence_factors = []
        
        # Spread quality (tighter spreads = higher confidence)
        if buy_book.spread_percentage and sell_book.spread_percentage:
            avg_spread = (buy_book.spread_percentage + sell_book.spread_percentage) / 2
            spread_confidence = max(0, 1 - (avg_spread / 2.0))  # 2% spread as reference
            confidence_factors.append(spread_confidence)
        
        # Order book depth
        buy_depth = sum(level.quantity * level.price for level in buy_book.asks[:5])
        sell_depth = sum(level.quantity * level.price for level in sell_book.bids[:5])
        avg_depth = (buy_depth + sell_depth) / 2
        depth_confidence = min(1.0, avg_depth / 10000)  # $10k as reference
        confidence_factors.append(depth_confidence)
        
        # Data freshness
        current_time = time.time()
        buy_age = current_time - buy_book.timestamp
        sell_age = current_time - sell_book.timestamp
        max_age = max(buy_age, sell_age)
        freshness_confidence = max(0, 1 - (max_age / 2.0))  # 2 seconds as reference
        confidence_factors.append(freshness_confidence)
        
        # Time synchronization
        time_diff = abs(buy_book.timestamp - sell_book.timestamp)
        sync_confidence = max(0, 1 - (time_diff / 1.0))  # 1 second as reference
        confidence_factors.append(sync_confidence)
        
        return sum(confidence_factors) / len(confidence_factors) if confidence_factors else 0.5
    
    async def _handle_opportunity(self, opportunity: ArbitrageOpportunity) -> None:
        """Handle discovered arbitrage opportunity."""
        try:
            # Check if we've seen this opportunity recently (avoid duplicates)
            opportunity_key = f"{opportunity.symbol}:{opportunity.buy_exchange}:{opportunity.sell_exchange}"
            
            if opportunity_key in self.recent_opportunities:
                last_time = self.recent_opportunities[opportunity_key]
                if time.time() - last_time < self.opportunity_cache_ttl:
                    return  # Skip duplicate
            
            # Update cache
            self.recent_opportunities[opportunity_key] = time.time()
            
            # Track statistics
            self.opportunities_found += 1
            
            # Log opportunity
            self.logger.info("Arbitrage opportunity found",
                           symbol=opportunity.symbol,
                           buy_exchange=opportunity.buy_exchange,
                           sell_exchange=opportunity.sell_exchange,
                           profit_percentage=opportunity.profit_percentage,
                           profit_absolute=opportunity.profit_absolute,
                           confidence=opportunity.confidence)
            
            # Notify handlers
            for handler in self.opportunity_handlers:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(opportunity)
                    else:
                        handler(opportunity)
                except Exception as e:
                    self.logger.error("Error in opportunity handler", error=str(e))
                    
        except Exception as e:
            self.logger.error("Error handling opportunity", error=str(e))
    
    def _cleanup_opportunity_cache(self) -> None:
        """Clean up old opportunities from cache."""
        current_time = time.time()
        expired_keys = [
            key for key, timestamp in self.recent_opportunities.items()
            if current_time - timestamp > self.opportunity_cache_ttl
        ]
        
        for key in expired_keys:
            del self.recent_opportunities[key]
    
    def get_statistics(self) -> Dict:
        """Get detector statistics."""
        return {
            "opportunities_found": self.opportunities_found,
            "scan_count": self.scan_count,
            "last_scan_time_ms": self.last_scan_time,
            "avg_scan_time_ms": self.last_scan_time,  # Simplified
            "cached_opportunities": len(self.recent_opportunities),
            "monitored_symbols": len(self.config.trading_pairs),
            "active_order_books": sum(len(books) for books in self.order_books.values())
        }
