"""
Triangular arbitrage detection for ArbitrageVision.

This module implements triangular arbitrage detection by finding profitable
currency cycles across trading pairs.
"""

import asyncio
import time
import uuid
from typing import Dict, List, Optional, Callable, Set, Tuple
from collections import defaultdict
import structlog

from src.config.settings import Settings
from src.data.models import ArbitrageOpportunity, ArbitrageType, OrderBookSnapshot, TriangularPath


class TriangularArbitrageDetector:
    """Triangular arbitrage opportunity detector."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.config = settings.triangular_arbitrage
        self.logger = structlog.get_logger("TriangularArbitrageDetector")
        
        # Current order books by exchange and symbol
        self.order_books: Dict[str, Dict[str, OrderBookSnapshot]] = defaultdict(dict)
        
        # Opportunity handlers
        self.opportunity_handlers: List[Callable[[ArbitrageOpportunity], None]] = []
        
        # Performance tracking
        self.opportunities_found = 0
        self.paths_evaluated = 0
        self.last_scan_time = 0
        
        # Running state
        self.running = False
        self.scan_task: Optional[asyncio.Task] = None
        
        # Triangular paths cache
        self.triangular_paths: List[List[str]] = []
        self._build_triangular_paths()
        
        # Exchange rate cache
        self.exchange_rates: Dict[str, Dict[str, float]] = defaultdict(dict)
        
    async def start(self) -> None:
        """Start the triangular arbitrage detector."""
        self.logger.info("Starting triangular arbitrage detector")
        
        self.running = True
        
        # Start scanning task
        self.scan_task = asyncio.create_task(self._scan_loop())
        
        self.logger.info("Triangular arbitrage detector started")
    
    async def stop(self) -> None:
        """Stop the triangular arbitrage detector."""
        self.logger.info("Stopping triangular arbitrage detector")
        
        self.running = False
        
        if self.scan_task:
            self.scan_task.cancel()
            try:
                await self.scan_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Triangular arbitrage detector stopped")
    
    def is_running(self) -> bool:
        """Check if detector is running."""
        return self.running
    
    def add_opportunity_handler(self, handler: Callable[[ArbitrageOpportunity], None]) -> None:
        """Add opportunity handler."""
        self.opportunity_handlers.append(handler)
    
    def remove_opportunity_handler(self, handler: Callable[[ArbitrageOpportunity], None]) -> None:
        """Remove opportunity handler."""
        if handler in self.opportunity_handlers:
            self.opportunity_handlers.remove(handler)
    
    async def process_update(self, order_book: OrderBookSnapshot) -> None:
        """Process order book update."""
        try:
            # Store order book
            self.order_books[order_book.exchange][order_book.symbol] = order_book
            
            # Update exchange rates
            self._update_exchange_rates(order_book)
            
            # Check for triangular arbitrage opportunities
            await self._check_triangular_arbitrage(order_book.exchange)
            
        except Exception as e:
            self.logger.error("Error processing order book update", 
                            exchange=order_book.exchange,
                            symbol=order_book.symbol,
                            error=str(e))
    
    async def _scan_loop(self) -> None:
        """Main scanning loop."""
        while self.running:
            try:
                start_time = time.perf_counter()
                
                # Scan all exchanges
                for exchange in self.order_books.keys():
                    await self._check_triangular_arbitrage(exchange)
                
                # Track performance
                scan_time = (time.perf_counter() - start_time) * 1000
                self.last_scan_time = scan_time
                
                if scan_time > 50:  # Log if scan takes more than 50ms
                    self.logger.warning("Slow triangular arbitrage scan", scan_time_ms=scan_time)
                
                # Wait for next scan
                await asyncio.sleep(self.config.path_timeout / 1000.0)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Error in scan loop", error=str(e))
                await asyncio.sleep(1)
    
    def _build_triangular_paths(self) -> None:
        """Build triangular arbitrage paths from configuration."""
        self.triangular_paths = []
        
        for triangle in self.config.triangles:
            if len(triangle) == 3:
                # Create all possible paths for the triangle
                currencies = triangle
                
                # Path 1: A -> B -> C -> A
                path1 = [
                    f"{currencies[0]}/{currencies[1]}",
                    f"{currencies[1]}/{currencies[2]}",
                    f"{currencies[2]}/{currencies[0]}"
                ]
                
                # Path 2: A -> C -> B -> A
                path2 = [
                    f"{currencies[0]}/{currencies[2]}",
                    f"{currencies[2]}/{currencies[1]}",
                    f"{currencies[1]}/{currencies[0]}"
                ]
                
                self.triangular_paths.extend([path1, path2])
        
        self.logger.info("Built triangular paths", count=len(self.triangular_paths))
    
    def _update_exchange_rates(self, order_book: OrderBookSnapshot) -> None:
        """Update exchange rates from order book."""
        if not (order_book.best_bid and order_book.best_ask):
            return
        
        symbol = order_book.symbol
        exchange = order_book.exchange
        
        # Store both directions
        mid_price = order_book.mid_price
        if mid_price:
            self.exchange_rates[exchange][symbol] = mid_price
            
            # Store inverse rate
            base, quote = symbol.split('/')
            inverse_symbol = f"{quote}/{base}"
            self.exchange_rates[exchange][inverse_symbol] = 1.0 / mid_price
    
    async def _check_triangular_arbitrage(self, exchange: str) -> None:
        """Check for triangular arbitrage opportunities on an exchange."""
        try:
            exchange_books = self.order_books.get(exchange, {})
            if len(exchange_books) < 3:
                return  # Need at least 3 pairs for triangular arbitrage
            
            # Check each triangular path
            for path in self.triangular_paths:
                await self._evaluate_triangular_path(exchange, path)
                
        except Exception as e:
            self.logger.error("Error checking triangular arbitrage", exchange=exchange, error=str(e))
    
    async def _evaluate_triangular_path(self, exchange: str, path: List[str]) -> None:
        """Evaluate a specific triangular arbitrage path."""
        try:
            self.paths_evaluated += 1
            
            # Check if all required pairs are available
            exchange_books = self.order_books[exchange]
            required_books = []
            
            for symbol in path:
                if symbol in exchange_books:
                    required_books.append(exchange_books[symbol])
                elif self._get_inverse_symbol(symbol) in exchange_books:
                    # Use inverse pair
                    required_books.append(exchange_books[self._get_inverse_symbol(symbol)])
                else:
                    return  # Missing required pair
            
            if len(required_books) != len(path):
                return
            
            # Calculate triangular arbitrage
            triangular_result = self._calculate_triangular_return(exchange, path, required_books)
            
            if triangular_result and triangular_result.is_profitable:
                # Create arbitrage opportunity
                opportunity = self._create_triangular_opportunity(exchange, triangular_result)
                if opportunity:
                    await self._handle_opportunity(opportunity)
                    
        except Exception as e:
            self.logger.error("Error evaluating triangular path", 
                            exchange=exchange, 
                            path=path, 
                            error=str(e))
    
    def _calculate_triangular_return(self, exchange: str, path: List[str], order_books: List[OrderBookSnapshot]) -> Optional[TriangularPath]:
        """Calculate return for a triangular arbitrage path."""
        try:
            starting_amount = 1000  # Start with $1000 equivalent
            current_amount = starting_amount
            rates = []
            quantities = []
            exchanges = [exchange] * len(path)
            
            for i, (symbol, book) in enumerate(zip(path, order_books)):
                # Determine if we're buying or selling
                base, quote = symbol.split('/')
                
                if book.symbol == symbol:
                    # Direct pair - we're buying base with quote
                    if book.best_ask:
                        rate = book.best_ask.price
                        quantity = min(current_amount / rate, book.best_ask.quantity)
                        current_amount = quantity
                        rates.append(rate)
                        quantities.append(quantity)
                    else:
                        return None
                else:
                    # Inverse pair - we're selling base for quote
                    if book.best_bid:
                        rate = 1.0 / book.best_bid.price
                        quantity = min(current_amount, book.best_bid.quantity)
                        current_amount = quantity * book.best_bid.price
                        rates.append(rate)
                        quantities.append(quantity)
                    else:
                        return None
            
            # Calculate total return
            total_return = current_amount / starting_amount
            profit = current_amount - starting_amount
            
            # Check minimum profit threshold
            profit_percentage = (profit / starting_amount) * 100
            if profit_percentage < self.config.min_profit_threshold * 100:
                return None
            
            # Check maximum profit threshold (likely error)
            if profit_percentage > self.config.max_profit_threshold * 100:
                return None
            
            return TriangularPath(
                currencies=[symbol.split('/') for symbol in path],
                exchanges=exchanges,
                rates=rates,
                quantities=quantities,
                total_return=total_return,
                profit=profit,
                timestamp=time.time()
            )
            
        except Exception as e:
            self.logger.error("Error calculating triangular return", error=str(e))
            return None
    
    def _create_triangular_opportunity(self, exchange: str, path: TriangularPath) -> Optional[ArbitrageOpportunity]:
        """Create arbitrage opportunity from triangular path."""
        try:
            # Use the first currency pair as the primary symbol
            primary_symbol = f"{path.currencies[0][0]}/{path.currencies[0][1]}"
            
            # Calculate effective buy and sell prices
            buy_price = path.rates[0] if path.rates else 0
            sell_price = path.total_return * buy_price if path.rates else 0
            
            # Use minimum quantity from the path
            quantity = min(path.quantities) if path.quantities else 0
            
            opportunity = ArbitrageOpportunity(
                id=str(uuid.uuid4()),
                type=ArbitrageType.TRIANGULAR,
                symbol=primary_symbol,
                buy_exchange=exchange,
                sell_exchange=exchange,  # Same exchange for triangular
                buy_price=buy_price,
                sell_price=sell_price,
                quantity=quantity,
                profit_absolute=path.profit,
                profit_percentage=(path.profit / (buy_price * quantity)) * 100 if buy_price * quantity > 0 else 0,
                timestamp=path.timestamp,
                confidence=self._calculate_triangular_confidence(path),
                metadata={
                    "path_currencies": path.currencies,
                    "path_rates": path.rates,
                    "path_quantities": path.quantities,
                    "total_return": path.total_return,
                    "arbitrage_type": "triangular"
                }
            )
            
            return opportunity
            
        except Exception as e:
            self.logger.error("Error creating triangular opportunity", error=str(e))
            return None
    
    def _calculate_triangular_confidence(self, path: TriangularPath) -> float:
        """Calculate confidence for triangular arbitrage."""
        confidence_factors = []
        
        # Profit magnitude (higher profit = higher confidence, but cap it)
        profit_confidence = min(1.0, path.profit / 100)  # $100 as reference
        confidence_factors.append(profit_confidence)
        
        # Quantity availability
        min_quantity = min(path.quantities) if path.quantities else 0
        quantity_confidence = min(1.0, min_quantity / 10)  # 10 units as reference
        confidence_factors.append(quantity_confidence)
        
        # Path complexity (shorter paths = higher confidence)
        complexity_confidence = max(0.5, 1.0 - (len(path.currencies) - 3) * 0.1)
        confidence_factors.append(complexity_confidence)
        
        return sum(confidence_factors) / len(confidence_factors) if confidence_factors else 0.5
    
    def _get_inverse_symbol(self, symbol: str) -> str:
        """Get inverse symbol (e.g., BTC/USD -> USD/BTC)."""
        base, quote = symbol.split('/')
        return f"{quote}/{base}"
    
    async def _handle_opportunity(self, opportunity: ArbitrageOpportunity) -> None:
        """Handle discovered triangular arbitrage opportunity."""
        try:
            # Track statistics
            self.opportunities_found += 1
            
            # Log opportunity
            self.logger.info("Triangular arbitrage opportunity found",
                           symbol=opportunity.symbol,
                           exchange=opportunity.buy_exchange,
                           profit_percentage=opportunity.profit_percentage,
                           profit_absolute=opportunity.profit_absolute,
                           confidence=opportunity.confidence,
                           path=opportunity.metadata.get("path_currencies"))
            
            # Notify handlers
            for handler in self.opportunity_handlers:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(opportunity)
                    else:
                        handler(opportunity)
                except Exception as e:
                    self.logger.error("Error in opportunity handler", error=str(e))
                    
        except Exception as e:
            self.logger.error("Error handling triangular opportunity", error=str(e))
    
    def get_statistics(self) -> Dict:
        """Get detector statistics."""
        return {
            "opportunities_found": self.opportunities_found,
            "paths_evaluated": self.paths_evaluated,
            "last_scan_time_ms": self.last_scan_time,
            "triangular_paths": len(self.triangular_paths),
            "active_exchanges": len(self.order_books),
            "cached_rates": sum(len(rates) for rates in self.exchange_rates.values())
        }
