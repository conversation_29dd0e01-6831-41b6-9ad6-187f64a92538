# Arbitrage Detection Configuration

# Simple Arbitrage Settings
simple_arbitrage:
  enabled: true
  min_profit_threshold: 0.001  # 0.1% minimum profit
  max_profit_threshold: 0.1    # 10% maximum profit (likely error)
  min_volume_threshold: 1000   # Minimum volume in USD
  max_position_size: 10000     # Maximum position size in USD
  
  # Price comparison settings
  price_tolerance: 0.0001      # Price comparison tolerance
  update_frequency: 100        # Update frequency in milliseconds
  
  # Risk parameters
  max_spread_age: 1000         # Maximum spread age in milliseconds
  min_liquidity_depth: 5       # Minimum order book depth levels
  
  # Supported trading pairs
  trading_pairs:
    - "BTC/USDT"
    - "ETH/USDT"
    - "LTC/USDT"
    - "BCH/USDT"
    - "ADA/USDT"
    - "DOT/USDT"
    - "LINK/USDT"
    - "XRP/USDT"

# Triangular Arbitrage Settings
triangular_arbitrage:
  enabled: true
  min_profit_threshold: 0.002  # 0.2% minimum profit
  max_profit_threshold: 0.05   # 5% maximum profit
  min_volume_threshold: 500    # Minimum volume in USD
  max_position_size: 5000      # Maximum position size in USD
  
  # Path finding settings
  max_path_length: 3           # Maximum triangular path length
  path_timeout: 500            # Path calculation timeout in milliseconds
  
  # Currency triangles to monitor
  triangles:
    - ["BTC", "ETH", "USDT"]
    - ["BTC", "LTC", "USDT"]
    - ["ETH", "ADA", "USDT"]
    - ["BTC", "BCH", "USDT"]
    - ["ETH", "DOT", "USDT"]
    - ["BTC", "LINK", "USDT"]
    - ["ETH", "LINK", "USDT"]

# Statistical Arbitrage Settings
statistical_arbitrage:
  enabled: true
  min_profit_threshold: 0.0005 # 0.05% minimum profit
  max_profit_threshold: 0.02   # 2% maximum profit
  min_volume_threshold: 2000   # Minimum volume in USD
  max_position_size: 20000     # Maximum position size in USD
  
  # Statistical parameters
  lookback_period: 3600        # Lookback period in seconds (1 hour)
  z_score_threshold: 2.0       # Z-score threshold for signals
  correlation_threshold: 0.8   # Minimum correlation threshold
  half_life_threshold: 300     # Maximum half-life in seconds
  
  # Mean reversion parameters
  entry_z_score: 2.0           # Z-score for entry
  exit_z_score: 0.5            # Z-score for exit
  stop_loss_z_score: 3.0       # Z-score for stop loss
  
  # Pairs to monitor
  pairs:
    - ["BTC/USDT", "ETH/USDT"]
    - ["BTC/USDT", "LTC/USDT"]
    - ["ETH/USDT", "ADA/USDT"]
    - ["BTC/USDT", "BCH/USDT"]
    - ["ETH/USDT", "DOT/USDT"]

# Cross-Exchange Basis Trading
basis_trading:
  enabled: false  # Advanced feature, disabled by default
  min_profit_threshold: 0.003  # 0.3% minimum profit
  max_position_size: 15000     # Maximum position size in USD
  
  # Futures/spot basis monitoring
  instruments:
    - spot: "BTC/USDT"
      futures: "BTC-PERP"
      exchanges: ["binance", "coinbase"]

# Global Risk Management
risk_management:
  # Position limits
  max_total_exposure: 100000   # Maximum total exposure in USD
  max_exchange_exposure: 25000 # Maximum exposure per exchange
  max_pair_exposure: 10000     # Maximum exposure per trading pair
  
  # Correlation limits
  max_correlation_exposure: 0.7 # Maximum correlation between positions
  correlation_window: 86400     # Correlation calculation window (24 hours)
  
  # Drawdown limits
  max_daily_drawdown: 0.05     # 5% maximum daily drawdown
  max_total_drawdown: 0.15     # 15% maximum total drawdown
  
  # Emergency stops
  emergency_stop_loss: 0.1     # 10% emergency stop loss
  circuit_breaker_threshold: 0.02 # 2% rapid loss threshold
  
  # Latency monitoring
  max_latency_threshold: 1000  # Maximum latency in milliseconds
  latency_window: 300          # Latency monitoring window in seconds

# Alert Configuration
alerts:
  # Profit thresholds for alerts
  high_profit_threshold: 0.01  # 1% profit opportunity
  medium_profit_threshold: 0.005 # 0.5% profit opportunity
  low_profit_threshold: 0.002  # 0.2% profit opportunity
  
  # Risk alerts
  high_risk_threshold: 0.8     # High risk score threshold
  medium_risk_threshold: 0.6   # Medium risk score threshold
  
  # System alerts
  connection_failure_alert: true
  latency_spike_alert: true
  data_quality_alert: true
  
  # Alert channels
  channels:
    - type: "webhook"
      url: "${WEBHOOK_URL}"
      enabled: true
    - type: "email"
      smtp_server: "${SMTP_SERVER}"
      enabled: false
    - type: "telegram"
      bot_token: "${TELEGRAM_BOT_TOKEN}"
      chat_id: "${TELEGRAM_CHAT_ID}"
      enabled: false

# Performance Optimization
performance:
  # Threading and async settings
  max_workers: 4               # Maximum worker threads
  async_batch_size: 100        # Async operation batch size
  
  # Memory management
  max_memory_usage: 2048       # Maximum memory usage in MB
  gc_threshold: 1800           # Garbage collection threshold in seconds
  
  # Caching settings
  cache_ttl: 300               # Cache TTL in seconds
  max_cache_size: 10000        # Maximum cache entries
  
  # Database settings
  db_connection_pool: 10       # Database connection pool size
  db_query_timeout: 30         # Database query timeout in seconds
