"""
Stream manager for ArbitrageVision.

This module coordinates real-time data streaming from multiple exchanges
and manages data flow to various components.
"""

import asyncio
import time
from typing import Dict, List, Optional, Callable, Set, Any
from collections import defaultdict, deque
import structlog

from src.config.settings import Settings
from src.exchanges.base import BaseExchange, OrderBook, Trade, ConnectionStatus
from src.exchanges.binance import BinanceExchange
from src.exchanges.coinbase import CoinbaseExchange
from src.exchanges.kraken import KrakenExchange
from src.exchanges.kucoin import KuCoinExchange
from src.data.order_book import OrderBookManager
from src.data.models import PerformanceMetrics


class StreamManager:
    """Manages real-time data streams from multiple exchanges."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = structlog.get_logger("StreamManager")
        
        # Exchange connectors
        self.exchanges: Dict[str, BaseExchange] = {}
        self.exchange_status: Dict[str, ConnectionStatus] = {}
        
        # Order book manager
        self.order_book_manager = OrderBookManager(
            max_depth=settings.simple_arbitrage.min_liquidity_depth * 2,
            max_history=1000
        )
        
        # Subscribers for different data types
        self.order_book_subscribers: List[Callable[[OrderBook], None]] = []
        self.trade_subscribers: List[Callable[[Trade], None]] = []
        self.status_subscribers: List[Callable[[str, ConnectionStatus], None]] = []
        
        # Performance tracking
        self.message_counts: Dict[str, int] = defaultdict(int)
        self.error_counts: Dict[str, int] = defaultdict(int)
        self.latency_measurements: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # Subscribed symbols
        self.subscribed_symbols: Set[str] = set()
        
        # Running state
        self.running = False
        self.tasks: List[asyncio.Task] = []
        
        # Health monitoring
        self.last_health_check = 0
        self.health_check_interval = 30  # seconds
        
    async def initialize(self) -> None:
        """Initialize the stream manager."""
        self.logger.info("Initializing stream manager")
        
        try:
            # Initialize exchange connectors
            await self._initialize_exchanges()
            
            # Set up order book manager subscribers
            self.order_book_manager.add_subscriber(self._handle_order_book_update)
            
            self.logger.info("Stream manager initialized successfully")
            
        except Exception as e:
            self.logger.error("Failed to initialize stream manager", error=str(e))
            raise
    
    async def start(self) -> None:
        """Start the stream manager."""
        self.logger.info("Starting stream manager")
        
        try:
            self.running = True
            
            # Start exchange connections
            for name, exchange in self.exchanges.items():
                if self.settings.is_exchange_enabled(name):
                    success = await exchange.start()
                    if success:
                        self.exchange_status[name] = ConnectionStatus.CONNECTED
                        self.logger.info("Exchange started", exchange=name)
                    else:
                        self.exchange_status[name] = ConnectionStatus.ERROR
                        self.logger.error("Failed to start exchange", exchange=name)
            
            # Start monitoring tasks
            self.tasks.append(asyncio.create_task(self._health_monitor_loop()))
            self.tasks.append(asyncio.create_task(self._performance_monitor_loop()))
            self.tasks.append(asyncio.create_task(self._cleanup_loop()))
            
            # Subscribe to configured symbols
            await self._subscribe_to_symbols()
            
            self.logger.info("Stream manager started successfully")
            
        except Exception as e:
            self.logger.error("Failed to start stream manager", error=str(e))
            await self.stop()
            raise
    
    async def stop(self) -> None:
        """Stop the stream manager."""
        self.logger.info("Stopping stream manager")
        
        self.running = False
        
        # Cancel all tasks
        for task in self.tasks:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
        
        # Stop all exchanges
        for exchange in self.exchanges.values():
            await exchange.stop()
        
        self.logger.info("Stream manager stopped")
    
    def is_running(self) -> bool:
        """Check if stream manager is running."""
        return self.running
    
    def add_subscriber(self, callback: Callable[[OrderBook], None]) -> None:
        """Add subscriber for order book updates."""
        self.order_book_subscribers.append(callback)
    
    def add_trade_subscriber(self, callback: Callable[[Trade], None]) -> None:
        """Add subscriber for trade updates."""
        self.trade_subscribers.append(callback)
    
    def add_status_subscriber(self, callback: Callable[[str, ConnectionStatus], None]) -> None:
        """Add subscriber for status updates."""
        self.status_subscribers.append(callback)
    
    def remove_subscriber(self, callback: Callable[[OrderBook], None]) -> None:
        """Remove order book subscriber."""
        if callback in self.order_book_subscribers:
            self.order_book_subscribers.remove(callback)
    
    def remove_trade_subscriber(self, callback: Callable[[Trade], None]) -> None:
        """Remove trade subscriber."""
        if callback in self.trade_subscribers:
            self.trade_subscribers.remove(callback)
    
    def remove_status_subscriber(self, callback: Callable[[str, ConnectionStatus], None]) -> None:
        """Remove status subscriber."""
        if callback in self.status_subscribers:
            self.status_subscribers.remove(callback)
    
    async def subscribe_symbol(self, symbol: str) -> bool:
        """Subscribe to a symbol across all exchanges."""
        try:
            self.subscribed_symbols.add(symbol)
            
            success_count = 0
            for name, exchange in self.exchanges.items():
                if self.settings.is_exchange_enabled(name) and exchange.is_connected:
                    # Subscribe to order book
                    if await exchange.subscribe_order_book(symbol):
                        success_count += 1
                    
                    # Subscribe to trades
                    await exchange.subscribe_trades(symbol)
            
            self.logger.info("Subscribed to symbol", symbol=symbol, exchanges=success_count)
            return success_count > 0
            
        except Exception as e:
            self.logger.error("Failed to subscribe to symbol", symbol=symbol, error=str(e))
            return False
    
    async def unsubscribe_symbol(self, symbol: str) -> bool:
        """Unsubscribe from a symbol across all exchanges."""
        try:
            self.subscribed_symbols.discard(symbol)
            
            for name, exchange in self.exchanges.items():
                if self.settings.is_exchange_enabled(name) and exchange.is_connected:
                    await exchange.unsubscribe_order_book(symbol)
                    await exchange.unsubscribe_trades(symbol)
            
            self.logger.info("Unsubscribed from symbol", symbol=symbol)
            return True
            
        except Exception as e:
            self.logger.error("Failed to unsubscribe from symbol", symbol=symbol, error=str(e))
            return False
    
    def get_exchange_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all exchanges."""
        status = {}
        for name, exchange in self.exchanges.items():
            info = exchange.get_exchange_info()
            status[name] = {
                "status": info.status.value,
                "latency": info.latency,
                "last_update": info.last_update,
                "supported_pairs": len(info.supported_pairs),
                "message_count": self.message_counts.get(name, 0),
                "error_count": self.error_counts.get(name, 0)
            }
        return status
    
    def get_performance_metrics(self) -> PerformanceMetrics:
        """Get current performance metrics."""
        current_time = time.time()
        
        # Calculate latency metrics
        latency_ms = {}
        for exchange, measurements in self.latency_measurements.items():
            if measurements:
                latency_ms[exchange] = sum(measurements) / len(measurements)
        
        # Calculate throughput
        throughput = {}
        for exchange, count in self.message_counts.items():
            # Simple throughput calculation (messages in last minute)
            throughput[exchange] = count / 60.0  # Approximate
        
        # Calculate error rates
        error_rate = {}
        for exchange in self.exchanges.keys():
            total_messages = self.message_counts.get(exchange, 0)
            errors = self.error_counts.get(exchange, 0)
            error_rate[exchange] = errors / max(total_messages, 1)
        
        return PerformanceMetrics(
            timestamp=current_time,
            latency_ms=latency_ms,
            throughput=throughput,
            error_rate=error_rate,
            memory_usage_mb=0,  # Would need psutil for actual measurement
            cpu_usage_percent=0,  # Would need psutil for actual measurement
            active_connections=len([e for e in self.exchanges.values() if e.is_connected]),
            processed_messages=sum(self.message_counts.values())
        )
    
    async def _initialize_exchanges(self) -> None:
        """Initialize exchange connectors."""
        for name, config in self.settings.get_enabled_exchanges().items():
            try:
                if name.lower() == "binance":
                    exchange = BinanceExchange(config)
                elif name.lower() == "coinbase":
                    exchange = CoinbaseExchange(config)
                elif name.lower() == "kraken":
                    exchange = KrakenExchange(config)
                elif name.lower() == "kucoin":
                    exchange = KuCoinExchange(config)
                else:
                    self.logger.warning("Unknown exchange type", exchange=name)
                    continue
                
                # Set up callbacks
                exchange.add_order_book_callback(self._handle_order_book)
                exchange.add_trade_callback(self._handle_trade)
                exchange.add_status_callback(self._handle_status_change)
                
                self.exchanges[name] = exchange
                self.exchange_status[name] = ConnectionStatus.DISCONNECTED
                
                self.logger.info("Exchange connector initialized", exchange=name)
                
            except Exception as e:
                self.logger.error("Failed to initialize exchange", exchange=name, error=str(e))
    
    async def _subscribe_to_symbols(self) -> None:
        """Subscribe to configured symbols."""
        # Get symbols from configuration
        symbols = set()
        symbols.update(self.settings.simple_arbitrage.trading_pairs)
        
        for symbol in symbols:
            await self.subscribe_symbol(symbol)
    
    async def _handle_order_book(self, order_book: OrderBook) -> None:
        """Handle order book update from exchange."""
        try:
            start_time = time.perf_counter()
            
            # Update order book manager
            await self.order_book_manager.update_order_book(order_book)
            
            # Track performance
            processing_time = (time.perf_counter() - start_time) * 1000
            self.latency_measurements[order_book.exchange].append(processing_time)
            self.message_counts[order_book.exchange] += 1
            
        except Exception as e:
            self.logger.error("Error handling order book update", 
                            exchange=order_book.exchange,
                            symbol=order_book.symbol,
                            error=str(e))
            self.error_counts[order_book.exchange] += 1
    
    async def _handle_order_book_update(self, order_book_snapshot) -> None:
        """Handle processed order book update."""
        # Notify subscribers
        for callback in self.order_book_subscribers:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(order_book_snapshot)
                else:
                    callback(order_book_snapshot)
            except Exception as e:
                self.logger.error("Error in order book subscriber", error=str(e))
    
    async def _handle_trade(self, trade: Trade) -> None:
        """Handle trade update from exchange."""
        try:
            self.message_counts[trade.exchange] += 1
            
            # Notify subscribers
            for callback in self.trade_subscribers:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(trade)
                    else:
                        callback(trade)
                except Exception as e:
                    self.logger.error("Error in trade subscriber", error=str(e))
                    
        except Exception as e:
            self.logger.error("Error handling trade update", 
                            exchange=trade.exchange,
                            symbol=trade.symbol,
                            error=str(e))
            self.error_counts[trade.exchange] += 1
    
    async def _handle_status_change(self, exchange_name: str, status: ConnectionStatus) -> None:
        """Handle exchange status change."""
        self.exchange_status[exchange_name] = status
        
        self.logger.info("Exchange status changed", 
                        exchange=exchange_name, 
                        status=status.value)
        
        # Notify subscribers
        for callback in self.status_subscribers:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(exchange_name, status)
                else:
                    callback(exchange_name, status)
            except Exception as e:
                self.logger.error("Error in status subscriber", error=str(e))
    
    async def _health_monitor_loop(self) -> None:
        """Monitor system health."""
        while self.running:
            try:
                await asyncio.sleep(self.health_check_interval)
                
                # Check exchange connections
                for name, exchange in self.exchanges.items():
                    if self.settings.is_exchange_enabled(name):
                        if not exchange.is_connected:
                            self.logger.warning("Exchange disconnected", exchange=name)
                
                # Clean up stale data
                self.order_book_manager.cleanup_stale_data()
                
                self.last_health_check = time.time()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Error in health monitor", error=str(e))
    
    async def _performance_monitor_loop(self) -> None:
        """Monitor performance metrics."""
        while self.running:
            try:
                await asyncio.sleep(60)  # Every minute
                
                metrics = self.get_performance_metrics()
                
                # Log performance summary
                self.logger.info("Performance metrics",
                               processed_messages=metrics.processed_messages,
                               active_connections=metrics.active_connections,
                               avg_latency=sum(metrics.latency_ms.values()) / len(metrics.latency_ms) if metrics.latency_ms else 0)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Error in performance monitor", error=str(e))
    
    async def _cleanup_loop(self) -> None:
        """Periodic cleanup of old data."""
        while self.running:
            try:
                await asyncio.sleep(300)  # Every 5 minutes
                
                # Reset counters periodically to prevent overflow
                current_time = time.time()
                if current_time - self.last_health_check > 3600:  # 1 hour
                    self.message_counts.clear()
                    self.error_counts.clear()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Error in cleanup loop", error=str(e))
